import argparse
import json
import logging
import os
import pathlib
import threading
import time
import traceback
from typing import Any, cast

import dash
import orjson
import pandas as pd
import plotly.io as pio
import utils_aws
import utils_general
from aws_lambda_typing import context as ctx
from aws_lambda_typing import events
from dash import html

from research_vol_dashboard.constants import LAMBDA_CONTEXT_ENV_NAME
from research_vol_dashboard.dashboard import get_charts
from research_vol_dashboard.event_handlers import populate_static_snapshots
from research_vol_dashboard.prep_data import prep_data
from research_vol_dashboard.retrieve_data import retrieve_data
from research_vol_dashboard.typings import (
    DashboardEventArgs,
    ResearchVolDashboard,
)

utils_general.setup_python_logger(os.getenv("LOG_LEVEL", logging.INFO))
pio.kaleido.scope.chromium_args += ("--single-process", "--no-zygote")
pd.options.mode.chained_assignment = None  # default='warn'


def _seppuku(timeout: int) -> None:
    time.sleep(timeout)
    os.kill(os.getpid(), 9)


def _select_event(
    arguments: argparse.Namespace,
) -> dict[str, Any]:
    event = {}
    event_path = pathlib.Path()
    if arguments.event_type == "static":
        event_path = (
            pathlib.Path(__file__).parent.resolve()
            / "events"
            / arguments.event_file
        )

    elif arguments.event_type == "flex":
        event_path = (
            pathlib.Path(__file__).parent.resolve() / arguments.event_file
        )
    else:
        raise NotImplementedError("Unrecognised Event type")

    with open(event_path) as file:
        event = json.load(file)

    return event["body"]


def _modify_static_event(
    event: dict[str, Any], arguments: argparse.Namespace
) -> dict[str, Any]:
    """
    Modifies the event if necessary based on the arguments provided.
    This function is called before _process_event.

    Args:
        event: The original event.
        arguments: Command-line arguments specifying how to modify the event.

    Returns:
        The modified event.
    """
    input_data = utils_general.json_loads(event)

    calc: ResearchVolDashboard = input_data["calc"]
    args = calc["args"]

    try:
        args = populate_static_snapshots(
            calc_args=args,
            target_snapshot=arguments.target_snapshot,
            target_expiry=arguments.listed_expiry,
            lookback_days=arguments.lookback_days,
        )
        # Update the args in the event
        calc["args"] = args
        input_data["calc"] = calc
    except Exception:
        logging.exception("Error while modifying objects in the input.json")
        raise

    return event


def _process_event(
    input_event: dict[str, Any],
) -> tuple[dict[str, pd.DataFrame], DashboardEventArgs]:
    try:
        calc: ResearchVolDashboard = input_event["calc"]
        args = calc["args"]

        currencies = args["currencies"]
        exchanges = args["exchanges"]
        models = args["models"]
        plot_objects = args["plot_objects"]
        periods = int(args["frequency"]["periods"])
        interval = args["frequency"]["interval"]
        output_version = calc["output_options"].get("version", "")

        consistent_read = cast(bool, args.get("consistent_read", False))

        start, end = utils_aws.get_calc_date_range(args["date_range"])

        time_of_start = time.time()
        raw_data = retrieve_data(
            start=start,
            end=end,
            currencies=currencies,
            plot_objects=plot_objects,
            models=models,
            periods=periods,
            interval=interval,
            consistent_read=consistent_read,
            version=output_version,
        )
        logging.info(
            f"Data retrieval took {time.time() - time_of_start:.2f}s",
        )

        time_of_start = time.time()
        formatted_data = prep_data(
            start=start,
            end=end,
            periods=periods,
            interval=interval,
            raw_data=raw_data,
            plot_objects=plot_objects,
        )
        logging.info(
            f"Data Preparation took {time.time() - time_of_start:.2f}s",
        )
        updated_event_args: DashboardEventArgs = {
            "end_time": args["date_range"]["absolute"]["end"],
            "start_time": start,
            "periods": periods,
            "currencies": currencies,
            "exchanges": exchanges,
            "models": models,
            "plot_objects": plot_objects,
            "output_version": output_version,
        }

    except Exception as e:
        logging.exception(f"Error e={e} while processing event={input_event}")
        raise

    return formatted_data, updated_event_args


def lambda_handler(
    event: events.APIGatewayProxyEventV1, context: ctx.Context
) -> Any:
    os.environ.setdefault(LAMBDA_CONTEXT_ENV_NAME, "True")
    input = utils_general.json_loads(event["body"])

    try:
        open_sockets = utils_general.socket_fds()
        logging.info(f"START: {len(open_sockets)} open sockets")

        formatted_data, event_args = _process_event(input)
        charts_dict = get_charts(
            event_args=event_args,
            plotting_data=formatted_data,
            encode_images=True,
        )

        if len(open_sockets) > 700:
            timeout = 2
            logging.info(
                f"Likely socket leak: there are {len(open_sockets)} open. Killing process in {timeout}s to avoid reaching lambda limit of 1024.",
            )
            threading.Thread(target=_seppuku, args=(timeout,)).start()

        return {
            "statusCode": 200,
            "headers": utils_aws.lambda_response_headers("GET"),
            "body": orjson.dumps(
                {
                    "input": input,
                    "output": {
                        "status": 0,
                        "msg": "Success",
                        "results": charts_dict,
                    },
                },
            ).decode("utf-8"),
        }
    except Exception:
        logging.exception(f"Error while processing event={event}")
        return {
            "statusCode": 500,
            "headers": utils_aws.lambda_response_headers("GET"),
            "body": orjson.dumps(
                {
                    "input": input,
                    "output": {
                        "status": 1,
                        "msg": traceback.format_exc(),
                        "results": "",
                    },
                }
            ).decode("utf-8"),
        }


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    parser.add_argument(
        "-event_type",
        type=str,
        default="flex",
        help="The type of .json file. Can be either static/flex",
    )

    parser.add_argument(
        "-target_snapshot",
        type=str,
        default="2025-08-08T14:00:00.00Z",
        help="ISO snapshot to plot",
    )

    parser.add_argument(
        "-data_version",
        type=str,
        default="",
        help="Version of the timeseries data to load",
    )

    parser.add_argument(
        "-listed_expiry",
        type=str,
        default="31JAN25",
        help="The listed expiry we want to plot",
    )

    parser.add_argument(
        "-lookback_days",
        type=int,
        default=30,
        help="Number of days to fetch historic timeseries data for",
    )

    parser.add_argument(
        "-event_file",
        type=str,
        default="events/test.json",
        help="The name of the .json suffixed event file to load",
    )

    return parser.parse_args()


def main() -> None:
    arguments = parse_args()

    event = _select_event(arguments)
    if arguments.event_type == "static":
        _modify_static_event(event, arguments)

    formatted_data, event_args = _process_event(event)
    grid_items = get_charts(event_args=event_args, plotting_data=formatted_data)

    app = dash.Dash(__name__)
    app.layout = html.Div(
        className="grid-container",
        children=grid_items,
    )
    app.run_server(debug=False)

    return


if __name__ == "__main__":
    os.environ.setdefault(LAMBDA_CONTEXT_ENV_NAME, "False")

    arguments = parse_args()

    event = _select_event(arguments)
    lambda_handler(event, {})

    # main()
