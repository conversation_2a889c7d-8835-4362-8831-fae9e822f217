{"requestContext": {"stage": "dev"}, "body": {"version": "1.0.0", "type": "<PERSON><PERSON><PERSON><PERSON>", "calc": {"args": {"debug": false, "exchanges": ["bybit"], "models": ["SVI"], "currencies": ["BTC"], "frequency": {"interval": "hour", "periods": 1}, "date_range": {"absolute": {"start": "_#ISO_START_ONE_MONTH_", "end": "_#ISO_END_"}}, "plot_objects": {"smiles": {"constant_tenor_exchange": "bybit", "models": ["SVI", "SABR"], "currencies": ["ETH"], "calc_types": ["smile"], "tenors": [30], "snapshot_timestamps": ["_#ISO_END_", "_#ISO_START_ONE_WEEK_"], "expiry_to_plot": "#_LISTED_EXPIRY_TARGET_", "listed_expiry_exchange": "bybit", "listed_expiry_timestamp": "_#ISO_END_"}, "volatility_term_structure": {"snapshot_timestamps": ["_#ISO_END_", "_#ISO_START_ONE_WEEK_"], "exchange": "bybit", "model": "SVI", "tenors": [7, 14, 30, 60, 90, 180, 365]}, "futures_term_structure": {"exchange": "bybit", "underlyings": ["ETH_USDC", "BTC_USDC"], "snapshot_timestamps": ["_#ISO_END_"], "tenors": [7, 14, 30, 60, 90, 180, 365]}, "v2timeseries": {"charts": {"Bybit BTC SVI ATM volatility": {"yaxis_title": "ATM Volatility", "chart_title": "BTC", "targets": [{"qualified_name": "bybit.option.BTC.SVI.7d.1h.smile", "target": "atm", "trace_title": "7d", "color": "#_7_"}, {"qualified_name": "bybit.option.BTC.SVI.14d.1h.smile", "target": "atm", "trace_title": "14d", "color": "#_14_"}, {"qualified_name": "bybit.option.BTC.SVI.30d.1h.smile", "target": "atm", "trace_title": "30d", "color": "#_30_"}, {"qualified_name": "bybit.option.BTC.SVI.90d.1h.smile", "target": "atm", "trace_title": "90d", "color": "#_90_"}, {"qualified_name": "bybit.option.BTC.SVI.180d.1h.smile", "target": "atm", "trace_title": "180d", "color": "#_180_"}]}, "Bybit ETH SVI ATM volatility": {"yaxis_title": "ATM Volatility", "chart_title": "ETH", "targets": [{"qualified_name": "bybit.option.ETH.SVI.7d.1h.smile", "target": "atm", "trace_title": "7d", "color": "#_7_"}, {"qualified_name": "bybit.option.ETH.SVI.14d.1h.smile", "target": "atm", "trace_title": "14d", "color": "#_14_"}, {"qualified_name": "bybit.option.ETH.SVI.30d.1h.smile", "target": "atm", "trace_title": "30d", "color": "#_30_"}, {"qualified_name": "bybit.option.ETH.SVI.90d.1h.smile", "target": "atm", "trace_title": "90d", "color": "#_90_"}, {"qualified_name": "bybit.option.ETH.SVI.180d.1h.smile", "target": "atm", "trace_title": "180d", "color": "#_180_"}]}, "Bybit BTC SVI PC skew surface": {"yaxis_title": "Skew", "chart_title": "BTC", "targets": [{"qualified_name": "bybit.option.BTC.SVI.7d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "7d", "color": "#_7_"}, {"qualified_name": "bybit.option.BTC.SVI.14d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "14d", "color": "#_14_"}, {"qualified_name": "bybit.option.BTC.SVI.30d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "30d", "color": "#_30_"}, {"qualified_name": "bybit.option.BTC.SVI.90d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "90d", "color": "#_90_"}, {"qualified_name": "bybit.option.BTC.SVI.180d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "180d", "color": "#_180_"}]}, "Bybit ETH SVI PC skew surface": {"yaxis_title": "Skew", "chart_title": "ETH", "targets": [{"qualified_name": "bybit.option.ETH.SVI.7d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "7d", "color": "#_7_"}, {"qualified_name": "bybit.option.ETH.SVI.14d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "14d", "color": "#_14_"}, {"qualified_name": "bybit.option.ETH.SVI.30d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "30d", "color": "#_30_"}, {"qualified_name": "bybit.option.ETH.SVI.90d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "90d", "color": "#_90_"}, {"qualified_name": "bybit.option.ETH.SVI.180d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "180d", "color": "#_180_"}]}, "Exchange_comparison_BTC_SVI_ATM_Vol": {"yaxis_title": "ATM Volatility", "chart_title": "BTC", "targets": [{"qualified_name": "bybit.option.BTC.SVI.30d.1h.smile", "target": "atm", "trace_title": "Bybit", "color": "#_BYBIT_COLOR"}, {"qualified_name": "v2composite.option.BTC.SVI.30d.1h.smile", "target": "atm", "trace_title": "Composite", "color": "#_V2COMPOSITE_COLOR"}, {"qualified_name": "v2lyra.option.BTC.SVI.30d.1h.smile", "target": "atm", "trace_title": "Lyra", "color": "#_V2LYRA_COLOR"}]}, "Exchange_comparison_ETH_SVI_ATM_Vol": {"yaxis_title": "ATM Volatility", "chart_title": "ETH", "targets": [{"qualified_name": "bybit.option.ETH.SVI.30d.1h.smile", "target": "atm", "trace_title": "Bybit", "color": "#_BYBIT_COLOR"}, {"qualified_name": "v2composite.option.ETH.SVI.30d.1h.smile", "target": "atm", "trace_title": "Composite", "color": "#_V2COMPOSITE_COLOR"}, {"qualified_name": "v2lyra.option.ETH.SVI.30d.1h.smile", "target": "atm", "trace_title": "Lyra", "color": "#_V2LYRA_COLOR"}]}, "Exchange Comparison Put-Call Skew BTC": {"yaxis_title": "Skew", "chart_title": "BTC", "targets": [{"qualified_name": "bybit.option.BTC.SVI.30d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "Bybit", "color": "#_BYBIT_COLOR"}, {"qualified_name": "v2composite.option.BTC.SVI.30d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "Composite", "color": "#_V2COMPOSITE_COLOR"}, {"qualified_name": "v2lyra.option.BTC.SVI.30d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "Lyra", "color": "#_V2LYRA_COLOR"}]}, "Exchange Comparison Put-Call Skew ETH": {"yaxis_title": "Skew", "chart_title": "ETH", "targets": [{"qualified_name": "bybit.option.ETH.SVI.30d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "Bybit", "color": "#_BYBIT_COLOR"}, {"qualified_name": "v2composite.option.ETH.SVI.30d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "Composite", "color": "#_V2COMPOSITE_COLOR"}, {"qualified_name": "v2lyra.option.ETH.SVI.30d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "Lyra", "color": "#_V2LYRA_COLOR"}]}, "Bybit BTC_USDC-Spot-Yields": {"yaxis_title": "Spot Yields", "chart_title": "BTC_USDC", "targets": [{"qualified_name": "bybit.future.BTC_USDC.7d.1h.annual.pct", "target": "pct", "trace_title": "7d", "color": "#_7_"}, {"qualified_name": "bybit.future.BTC_USDC.14d.1h.annual.pct", "target": "pct", "trace_title": "14d", "color": "#_14_"}, {"qualified_name": "bybit.future.BTC_USDC.30d.1h.annual.pct", "target": "pct", "trace_title": "30d", "color": "#_30_"}, {"qualified_name": "bybit.future.BTC_USDC.90d.1h.annual.pct", "target": "pct", "trace_title": "90d", "color": "#_90_"}, {"qualified_name": "bybit.future.BTC_USDC.180d.1h.annual.pct", "target": "pct", "trace_title": "180d", "color": "#_180_"}]}, "Bybit ETH_USDC-Spot-Yields": {"yaxis_title": "Spot Yields", "chart_title": "ETH_USDC", "targets": [{"qualified_name": "bybit.future.ETH_USDC.7d.1h.annual.pct", "target": "pct", "trace_title": "7d", "color": "#_7_"}, {"qualified_name": "bybit.future.ETH_USDC.14d.1h.annual.pct", "target": "pct", "trace_title": "14d", "color": "#_14_"}, {"qualified_name": "bybit.future.ETH_USDC.30d.1h.annual.pct", "target": "pct", "trace_title": "30d", "color": "#_30_"}, {"qualified_name": "bybit.future.ETH_USDC.90d.1h.annual.pct", "target": "pct", "trace_title": "90d", "color": "#_90_"}, {"qualified_name": "bybit.future.ETH_USDC.180d.1h.annual.pct", "target": "pct", "trace_title": "180d", "color": "#_180_"}]}, "Bybit BTC Perpetuals": {"yaxis_title": "BTC Perpetuals", "chart_title": "BTC", "chart_type": "bar", "targets": [{"qualified_name": "bybit.perpetual.BTCUSDT.1h.funding.rate", "target": "rate", "color": "#_BYBIT_COLOR"}]}, "Bybit ETH Perpetuals": {"yaxis_title": "ETH Perpetuals", "chart_title": "ETH", "chart_type": "bar", "targets": [{"qualified_name": "bybit.perpetual.ETHUSDT.1h.funding.rate", "target": "rate", "color": "#_BYBIT_COLOR"}]}, "Bybit TON Perpetuals": {"yaxis_title": "TON Perpetuals", "chart_title": "TON", "chart_type": "bar", "targets": [{"qualified_name": "bybit.perpetual.TONUSDT.1h.funding.rate", "target": "rate", "color": "#_BYBIT_COLOR"}]}, "Bybit CRV Perpetuals": {"yaxis_title": "CRV Perpetuals", "chart_title": "CRV", "chart_type": "bar", "targets": [{"qualified_name": "bybit.perpetual.CRVUSDT.1h.funding.rate", "target": "rate", "color": "#_BYBIT_COLOR"}]}, "Bybit SOL Perpetuals": {"yaxis_title": "SOL Perpetuals", "chart_title": "SOL", "chart_type": "bar", "targets": [{"qualified_name": "bybit.perpetual.SOLUSDT.1h.funding.rate", "target": "rate", "color": "#_BYBIT_COLOR"}]}, "Bybit ATOM Perpetuals": {"yaxis_title": "ATOM Perpetuals", "chart_title": "ATOM", "chart_type": "bar", "targets": [{"qualified_name": "bybit.perpetual.ATOMUSDT.1h.funding.rate", "target": "rate", "color": "#_BYBIT_COLOR"}]}, "Bybit NEAR Perpetuals": {"yaxis_title": "NEAR Perpetuals", "chart_title": "NEAR", "chart_type": "bar", "targets": [{"qualified_name": "bybit.perpetual.NEARUSDT.1h.funding.rate", "target": "rate", "color": "#_BYBIT_COLOR"}]}, "Bybit BNB Perpetuals": {"yaxis_title": "BNB Perpetuals", "chart_title": "BNB", "chart_type": "bar", "targets": [{"qualified_name": "bybit.perpetual.BNBUSDT.1h.funding.rate", "target": "rate", "color": "#_BYBIT_COLOR"}]}, "Bybit ADA Perpetuals": {"yaxis_title": "ADA Perpetuals", "chart_title": "ADA", "chart_type": "bar", "targets": [{"qualified_name": "bybit.perpetual.ADAUSDT.1h.funding.rate", "target": "rate", "color": "#_BYBIT_COLOR"}]}, "Bybit XRP Perpetuals": {"yaxis_title": "XRP Perpetuals", "chart_title": "XRP", "chart_type": "bar", "targets": [{"qualified_name": "bybit.perpetual.XRPUSDT.1h.funding.rate", "target": "rate", "color": "#_BYBIT_COLOR"}]}, "Bybit DOGE Perpetuals": {"yaxis_title": "DOGE Perpetuals", "chart_title": "DOGE", "chart_type": "bar", "targets": [{"qualified_name": "bybit.perpetual.DOGEUSDT.1h.funding.rate", "target": "rate", "color": "#_BYBIT_COLOR"}]}}}, "v2smiles": {"charts": {"Exchange_comparison_BTC_SMILES.moneyness": {"yaxis_title": "ATM Volatility", "chart_title": "BTC -- SVI -- 30D Tenor -- _#ISO_END_HOUR_ UTC Snapshot", "targets": [{"tenor": 30, "exchange": "bybit", "currency": "BTC", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "Bybit", "color": "#F7A600"}, {"tenor": 30, "exchange": "v2composite", "currency": "BTC", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "Market Composite", "color": "#247CFF"}, {"tenor": 30, "exchange": "v2lyra", "currency": "BTC", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "Lyra", "color": "#25fab0"}]}, "Exchange_comparison_ETH_SMILES.moneyness": {"yaxis_title": "ATM Volatility", "chart_title": "ETH -- SVI -- 30D Tenor -- 09:00 UTC Snapshot", "targets": [{"tenor": 30, "exchange": "bybit", "currency": "ETH", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "Bybit", "color": "#F7A600"}, {"tenor": 30, "exchange": "v2composite", "currency": "ETH", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": " Market Composite", "color": "#247CFF"}, {"tenor": 30, "exchange": "v2lyra", "currency": "ETH", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "Lyra", "color": "#25fab0"}]}, "Bybit_ETH_SMILES.moneyness": {"yaxis_title": "ATM Volatility", "chart_title": "Bybit Surface -- ETH -- SVI -- _#ISO_END_HOUR_ UTC Snapshot", "targets": [{"tenor": 7, "exchange": "bybit", "currency": "ETH", "color_gradient": "False", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "7d", "color": "#247CFF"}, {"tenor": 14, "exchange": "bybit", "currency": "ETH", "color_gradient": "False", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "14d", "color": "#C5C5C5"}, {"tenor": 30, "exchange": "bybit", "currency": "ETH", "color_gradient": "False", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "30d", "color": "#FFCD00"}, {"tenor": 90, "exchange": "bybit", "currency": "ETH", "color_gradient": "False", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "90d", "color": "#E16100"}, {"tenor": 180, "exchange": "bybit", "currency": "ETH", "color_gradient": "False", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "180d", "color": "#43FF64"}]}, "Bybit_BTC_SMILES.moneyness": {"yaxis_title": "ATM Volatility", "chart_title": "Bybit Surface -- BTC -- SVI -- _#ISO_END_HOUR_ UTC Snapshot", "targets": [{"tenor": 7, "exchange": "bybit", "currency": "BTC", "color_gradient": "False", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "7d", "color": "#247CFF"}, {"tenor": 14, "exchange": "bybit", "currency": "BTC", "color_gradient": "False", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "14d", "color": "#C5C5C5"}, {"tenor": 30, "exchange": "bybit", "currency": "BTC", "color_gradient": "False", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "30d", "color": "#FFCD00"}, {"tenor": 90, "exchange": "bybit", "currency": "BTC", "color_gradient": "False", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "90d", "color": "#E16100"}, {"tenor": 180, "exchange": "bybit", "currency": "BTC", "color_gradient": "False", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "180d", "color": "#43FF64"}]}}}, "aggregate_instruments": {"charts": {"BTC_28MAR25": {"yaxis_title": "Volume", "chart_title": "Bybit Tester", "targets": [{"exchange": "bybit", "instrument": "28MAR25", "type": "option", "baseAsset": "ETH", "quoteAsset": "ETH", "trace_title": "volume", "color": "#FF2200", "target_suffix": "volume"}, {"exchange": "bybit", "instrument": "28MAR25", "type": "option", "baseAsset": "BTC", "quoteAsset": "BTC", "trace_title": "volume", "color": "#43FF64", "target_suffix": "volume"}]}}}, "instruments_aggregator": {"charts": {"Bybit Perpetuals": {"yaxis_title": "OI", "chart_title": "Bybit Perpetuals", "aggregation_level": "currency", "asset_type": "perpetual", "currencies": ["ETH", "BTC"], "filters": {"exchanges": [{"exchange": "bybit", "original_quote_asset": "USDC"}, {"exchange": "bybit", "original_quote_asset": "USDT"}]}, "target_suffix": "open_interest", "chart_formats": {"dollar_denomination": true, "chart_type": "stacked_area"}, "resample_frequency": "hour"}, "Bybit Futures Open Interest": {"yaxis_title": "OI", "chart_title": "Bybit Futures Open Interest", "aggregation_level": "currency", "sub_aggregation_level": "expiry", "asset_type": "future", "currencies": ["BTC", "ETH"], "filters": {"exchanges": [{"exchange": "bybit", "original_quote_asset": "USDC"}]}, "target_suffix": "open_interest", "chart_formats": {"show_full_legend": false, "dollar_denomination": true, "chart_type": "stacked_area"}, "resample_frequency": "hour"}, "Bybit Futures Volumes": {"yaxis_title": "Volume", "chart_title": "Bybit Futures Volumes", "aggregation_level": "currency", "sub_aggregation_level": "expiry", "asset_type": "future", "currencies": ["BTC", "ETH"], "filters": {"exchanges": [{"exchange": "bybit", "original_quote_asset": "USDC"}]}, "target_suffix": "volume", "chart_formats": {"show_full_legend": false, "dollar_denomination": true, "chart_type": "bar"}, "resample_frequency": "day"}, "Bybit USDT Perpetuals Volumes": {"yaxis_title": "Volume", "chart_title": "Bybit USDT Perpetual Volumes", "aggregation_level": "currency", "asset_type": "perpetual", "currencies": ["BTC", "ETH"], "filters": {"exchanges": [{"exchange": "bybit", "original_quote_asset": "USDT"}, {"exchange": "bybit", "original_quote_asset": "USDC"}]}, "target_suffix": "volume", "chart_formats": {"dollar_denomination": true, "chart_type": "bar"}, "resample_frequency": "day"}, "Bybit BTC Options Volumes": {"yaxis_title": "Volume", "chart_title": "Bybit BTC Option Volumes", "aggregation_level": "option_type", "sub_aggregation_level": "expiry", "asset_type": "option", "currencies": ["BTC"], "filters": {"exchanges": [{"exchange": "bybit", "original_quote_asset": "USDC"}], "currencies": ["BTC"]}, "target_suffix": "volume", "chart_formats": {"dollar_denomination": true, "chart_type": "bar", "invert_aggregation_levels": ["P"], "show_full_legend": false}, "resample_frequency": "day"}, "Bybit ETH Options Volumes": {"yaxis_title": "Volume", "chart_title": "Bybit ETH Option Volumes", "aggregation_level": "option_type", "sub_aggregation_level": "expiry", "asset_type": "option", "currencies": ["ETH"], "filters": {"exchanges": [{"exchange": "bybit", "original_quote_asset": "USDC"}], "currencies": ["ETH"]}, "target_suffix": "volume", "chart_formats": {"dollar_denomination": true, "chart_type": "bar", "invert_aggregation_levels": ["P"], "show_full_legend": false}, "resample_frequency": "day"}, "Bybit ETH Futures OI": {"yaxis_title": "OI", "chart_title": "Bybit ETH Futures OI", "aggregation_level": "expiry", "asset_type": "future", "currencies": ["ETH"], "filters": {"exchanges": [{"exchange": "bybit", "original_quote_asset": "USDC"}], "currencies": ["ETH"]}, "target_suffix": "open_interest", "chart_formats": {"dollar_denomination": true, "chart_type": "stacked_area"}, "resample_frequency": "hour"}, "Bybit BTC Futures OI": {"yaxis_title": "OI", "chart_title": "Bybit BTC Futures OI", "aggregation_level": "expiry", "asset_type": "future", "currencies": ["BTC"], "filters": {"exchanges": [{"exchange": "bybit", "original_quote_asset": "USDC"}], "currencies": ["BTC"]}, "target_suffix": "open_interest", "chart_formats": {"dollar_denomination": true, "chart_type": "stacked_area"}, "resample_frequency": "hour"}, "Bybit ETH Options OI": {"yaxis_title": "OI", "chart_title": "Bybit ETH Options OI", "aggregation_level": "option_type", "sub_aggregation_level": "expiry", "asset_type": "option", "currencies": ["ETH"], "filters": {"exchanges": [{"exchange": "bybit", "original_quote_asset": "USDC"}], "currencies": ["ETH"]}, "target_suffix": "open_interest", "chart_formats": {"dollar_denomination": true, "chart_type": "stacked_area", "invert_aggregation_levels": ["P"], "show_full_legend": false}, "resample_frequency": "hour"}, "Bybit BTC Options OI": {"yaxis_title": "OI", "chart_title": "Bybit BTC Options OI", "aggregation_level": "option_type", "sub_aggregation_level": "expiry", "asset_type": "option", "currencies": ["BTC"], "filters": {"exchanges": [{"exchange": "bybit", "original_quote_asset": "USDC"}], "currencies": ["BTC"]}, "target_suffix": "open_interest", "chart_formats": {"dollar_denomination": true, "chart_type": "stacked_area", "invert_aggregation_levels": ["P"], "show_full_legend": false}, "resample_frequency": "hour"}}}}}, "output_options": {"type": "csv", "format": "timeseries", "version": "v-00004"}}}}