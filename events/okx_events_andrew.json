{"body": {"version": "1.0.0", "type": "ResearchVolDashboard", "calc": {"args": {"debug": false, "exchanges": ["okx"], "models": ["SABR"], "currencies": ["ETH", "BTC"], "frequency": {"interval": "hour", "periods": 1}, "date_range": {"absolute": {"start": "_#ISO_START_ONE_MONTH_", "end": "_#ISO_END_"}}, "plot_objects": {"smiles": {"constant_tenor_exchange": "okx", "models": ["SVI", "SABR"], "currencies": ["ETH", "BTC"], "calc_types": ["smile"], "tenors": [30], "snapshot_timestamps": ["_#ISO_END_", "_#ISO_START_ONE_WEEK_"], "expiry_to_plot": "#_LISTED_EXPIRY_TARGET_", "listed_expiry_exchange": "okx", "listed_expiry_timestamp": "_#ISO_END_"}, "v2timeseries": {"charts": {"OKX BTC SABR ATM volatility": {"yaxis_title": "ATM Volatility", "chart_title": "BTC", "targets": [{"qualified_name": "okx.option.BTC.SABR.7d.1h.smile", "target": "atm", "trace_title": "7d", "color": "#_7_"}, {"qualified_name": "okx.option.BTC.SABR.14d.1h.smile", "target": "atm", "trace_title": "14d", "color": "#_14_"}, {"qualified_name": "okx.option.BTC.SABR.30d.1h.smile", "target": "atm", "trace_title": "30d", "color": "#_30_"}, {"qualified_name": "okx.option.BTC.SABR.90d.1h.smile", "target": "atm", "trace_title": "90d", "color": "#_90_"}]}, "OKX ETH SABR ATM volatility": {"yaxis_title": "ATM Volatility", "chart_title": "ETH", "targets": [{"qualified_name": "okx.option.ETH.SABR.7d.1h.smile", "target": "atm", "trace_title": "7d", "color": "#_7_"}, {"qualified_name": "okx.option.ETH.SABR.14d.1h.smile", "target": "atm", "trace_title": "14d", "color": "#_14_"}, {"qualified_name": "okx.option.ETH.SABR.30d.1h.smile", "target": "atm", "trace_title": "30d", "color": "#_30_"}, {"qualified_name": "okx.option.ETH.SABR.90d.1h.smile", "target": "atm", "trace_title": "90d", "color": "#_90_"}]}, "OKX BTC SVI ATM volatility": {"yaxis_title": "ATM Volatility", "chart_title": "BTC", "targets": [{"qualified_name": "okx.option.BTC.SVI.7d.1h.smile", "target": "atm", "trace_title": "7d", "color": "#_7_"}, {"qualified_name": "okx.option.BTC.SVI.14d.1h.smile", "target": "atm", "trace_title": "14d", "color": "#_14_"}, {"qualified_name": "okx.option.BTC.SVI.30d.1h.smile", "target": "atm", "trace_title": "30d", "color": "#_30_"}, {"qualified_name": "okx.option.BTC.SVI.90d.1h.smile", "target": "atm", "trace_title": "90d", "color": "#_90_"}]}, "OKX ETH SVI ATM volatility": {"yaxis_title": "ATM Volatility", "chart_title": "ETH", "targets": [{"qualified_name": "okx.option.ETH.SVI.7d.1h.smile", "target": "atm", "trace_title": "7d", "color": "#_7_"}, {"qualified_name": "okx.option.ETH.SVI.30d.1h.smile", "target": "atm", "trace_title": "30d", "color": "#_30_"}, {"qualified_name": "okx.option.ETH.SVI.90d.1h.smile", "target": "atm", "trace_title": "90d", "color": "#_90_"}]}, "OKX Currency-Combined-1M-ATM-VOL-ETH": {"yaxis_title": "1M-ATM Implied Volatility", "chart_title": "", "targets": [{"qualified_name": "okx.option.BTC.SVI.30d.1h.smile", "target": "atm", "trace_title": "BTC", "color": "#_PERP_BTC_USD_COLOR"}, {"qualified_name": "okx.option.ETH.SVI.30d.1h.smile", "target": "atm", "trace_title": "ETH", "color": "#_PERP_ETH_USD_COLOR"}]}, "OKX BTC SVI PC skew": {"yaxis_title": "Skew", "chart_title": "BTC", "targets": [{"qualified_name": "okx.option.BTC.SVI.7d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "7d", "color": "#_7_"}, {"qualified_name": "okx.option.BTC.SVI.14d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "14d", "color": "#_14_"}, {"qualified_name": "okx.option.BTC.SVI.30d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "30d", "color": "#_30_"}, {"qualified_name": "okx.option.BTC.SVI.90d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "90d", "color": "#_90_"}]}, "OKX ETH SVI PC skew": {"yaxis_title": "Skew", "chart_title": "ETH", "targets": [{"qualified_name": "okx.option.ETH.SVI.7d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "7d", "color": "#_7_"}, {"qualified_name": "okx.option.ETH.SVI.30d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "30d", "color": "#_30_"}, {"qualified_name": "okx.option.ETH.SVI.90d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "90d", "color": "#_90_"}]}}}, "v2smiles": {"charts": {"okx_snap_BTC_SMILES.moneyness": {"yaxis_title": "ATM Volatility", "chart_title": "OKX Surface -- BTC -- SVI -- _#ISO_END_HOUR_ UTC Snapshot", "targets": [{"tenor": 7, "exchange": "okx", "currency": "BTC", "color_gradient": "False", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "7d", "color": "#247CFF"}, {"tenor": 14, "exchange": "okx", "currency": "BTC", "color_gradient": "False", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "14d", "color": "#C5C5C5"}, {"tenor": 30, "exchange": "okx", "currency": "BTC", "color_gradient": "False", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "30d", "color": "#FFCD00"}, {"tenor": 90, "exchange": "okx", "currency": "BTC", "color_gradient": "False", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "90d", "color": "#E16100"}]}, "okx_snap_ETH_SMILES.moneyness": {"yaxis_title": "ATM Volatility", "chart_title": "OKX Surface -- ETH -- SVI -- _#ISO_END_HOUR_ UTC Snapshot", "targets": [{"tenor": 7, "exchange": "okx", "currency": "ETH", "color_gradient": "False", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "7d", "color": "#247CFF"}, {"tenor": 14, "exchange": "okx", "currency": "ETH", "color_gradient": "False", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "14d", "color": "#C5C5C5"}, {"tenor": 30, "exchange": "okx", "currency": "ETH", "color_gradient": "False", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "30d", "color": "#FFCD00"}, {"tenor": 90, "exchange": "okx", "currency": "ETH", "color_gradient": "False", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "90d", "color": "#E16100"}]}}}, "volatility_term_structure": {"snapshot_timestamps": ["_#ISO_END_", "_#ISO_START_ONE_WEEK_"], "exchange": "okx", "model": "SVI", "tenors": [7, 14, 30, 60, 90, 365]}, "futures_term_structure": {"exchange": "okx", "snapshot_timestamps": ["_#ISO_END_"], "underlyings": ["BTC_USD", "ETH_USD"], "tenors": [7, 14, 30, 60, 90, 180, 365]}}}, "output_options": {"type": "csv", "format": "timeseries", "version": "v-00004"}}}}