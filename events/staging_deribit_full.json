{"requestContext": {"stage": "dev"}, "body": {"version": "1.0.0", "type": "<PERSON><PERSON><PERSON><PERSON>", "calc": {"args": {"debug": false, "exchanges": ["deribit"], "models": ["SVI"], "currencies": ["BTC"], "frequency": {"interval": "hour", "periods": 1}, "series_to_plot": "atm", "date_range": {"absolute": {"start": "_#ISO_START_ONE_MONTH_", "end": "_#ISO_END_"}}, "plot_objects": {"instruments_aggregator": {"charts": {"deribit BTC Options Volumes": {"yaxis_title": "Volume", "chart_title": "deribit BTC Option Volumes", "aggregation_level": "option_type", "sub_aggregation_level": "expiry", "asset_type": "option", "currencies": ["BTC"], "filters": {"exchanges": [{"exchange": "deribit", "original_quote_asset": "BTC"}], "currencies": ["BTC"]}, "target_suffix": "volume", "chart_formats": {"dollar_denomination": true, "chart_type": "bar", "invert_aggregation_levels": ["P"], "show_full_legend": false}, "resample_frequency": "day"}, "deribit ETH Options Volumes": {"yaxis_title": "Volume", "chart_title": "deribit ETH Option Volumes", "aggregation_level": "option_type", "sub_aggregation_level": "expiry", "asset_type": "option", "currencies": ["ETH"], "filters": {"exchanges": [{"exchange": "deribit", "original_quote_asset": "ETH"}], "currencies": ["ETH"]}, "target_suffix": "volume", "chart_formats": {"dollar_denomination": true, "chart_type": "bar", "invert_aggregation_levels": ["P"], "show_full_legend": false}, "resample_frequency": "day"}, "deribit SOL Options Volumes": {"yaxis_title": "Volume", "chart_title": "deribit SOL Option Volumes", "aggregation_level": "option_type", "sub_aggregation_level": "expiry", "asset_type": "option", "currencies": ["SOL"], "filters": {"exchanges": [{"exchange": "deribit", "original_quote_asset": "USDC"}], "currencies": ["SOL"]}, "target_suffix": "volume", "chart_formats": {"dollar_denomination": true, "chart_type": "bar", "invert_aggregation_levels": ["P"], "show_full_legend": false}, "resample_frequency": "day"}, "deribit BTC Options OI": {"yaxis_title": "OI", "chart_title": "deribit BTC Options OI", "aggregation_level": "option_type", "sub_aggregation_level": "expiry", "asset_type": "option", "currencies": ["BTC"], "filters": {"exchanges": [{"exchange": "deribit", "original_quote_asset": "BTC"}], "currencies": ["BTC"]}, "target_suffix": "open_interest", "chart_formats": {"dollar_denomination": true, "chart_type": "stacked_area", "invert_aggregation_levels": ["P"], "show_full_legend": false}, "resample_frequency": "hour"}, "deribit ETH Options OI": {"yaxis_title": "OI", "chart_title": "deribit ETH Options OI", "aggregation_level": "option_type", "sub_aggregation_level": "expiry", "asset_type": "option", "currencies": ["ETH"], "filters": {"exchanges": [{"exchange": "deribit", "original_quote_asset": "ETH"}], "currencies": ["ETH"]}, "target_suffix": "open_interest", "chart_formats": {"dollar_denomination": true, "chart_type": "stacked_area", "invert_aggregation_levels": ["P"], "show_full_legend": false}, "resample_frequency": "hour"}, "deribit SOL Options OI": {"yaxis_title": "OI", "chart_title": "deribit SOL Options OI", "aggregation_level": "option_type", "sub_aggregation_level": "expiry", "asset_type": "option", "currencies": ["SOL"], "filters": {"exchanges": [{"exchange": "deribit", "original_quote_asset": "USDC"}], "currencies": ["SOL"]}, "target_suffix": "open_interest", "chart_formats": {"dollar_denomination": true, "chart_type": "stacked_area", "invert_aggregation_levels": ["P"], "show_full_legend": false}, "resample_frequency": "hour"}}}}}, "output_options": {"type": "csv", "format": "timeseries", "version": ""}}}}