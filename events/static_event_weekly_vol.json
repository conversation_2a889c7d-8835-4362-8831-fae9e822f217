{"body": {"version": "1.0.0", "type": "ResearchVolDashboard", "calc": {"args": {"debug": false, "exchanges": ["deribit"], "models": ["SVI"], "currencies": ["ETH", "BTC"], "frequency": {"interval": "hour", "periods": 1}, "date_range": {"absolute": {"start": "_#ISO_START_ONE_MONTH_", "end": "_#ISO_END_"}}, "plot_objects": {"perpetual": {"exchange": "deribit"}, "smiles": {"constant_tenor_exchange": "deribit", "models": ["SVI", "SABR"], "currencies": ["ETH"], "calc_types": ["smile"], "tenors": [30], "snapshot_timestamps": ["_#ISO_END_", "_#ISO_START_ONE_WEEK_"], "expiry_to_plot": "#_LISTED_EXPIRY_TARGET_", "listed_expiry_exchange": "deribit", "listed_expiry_timestamp": "_#ISO_END_"}, "volatility_term_structure": {"snapshot_timestamps": ["_#ISO_END_", "_#ISO_START_ONE_WEEK_"], "exchange": "deribit", "model": "SVI", "tenors": [7, 14, 30, 60, 90, 180, 365]}, "futures_term_structure": {"exchange": "deribit", "snapshot_timestamps": ["_#ISO_END_"], "underlyings": ["BTC_USD", "ETH_USD"], "tenors": [7, 14, 30, 60, 90, 180, 365]}, "v2timeseries": {"charts": {"F9_deribit BTC SVI ATM volatility": {"yaxis_title": "ATM Volatility", "chart_title": "BTC", "targets": [{"qualified_name": "deribit.option.BTC.SVI.7d.1h.smile", "target": "atm", "trace_title": "7d", "color": "#_7_"}, {"qualified_name": "deribit.option.BTC.SVI.14d.1h.smile", "target": "atm", "trace_title": "14d", "color": "#_14_"}, {"qualified_name": "deribit.option.BTC.SVI.30d.1h.smile", "target": "atm", "trace_title": "30d", "color": "#_30_"}, {"qualified_name": "deribit.option.BTC.SVI.90d.1h.smile", "target": "atm", "trace_title": "90d", "color": "#_90_"}, {"qualified_name": "deribit.option.BTC.SVI.180d.1h.smile", "target": "atm", "trace_title": "180d", "color": "#_180_"}]}, "F12_deribit ETH SVI ATM volatility": {"yaxis_title": "ATM Volatility", "chart_title": "ETH", "targets": [{"qualified_name": "deribit.option.ETH.SVI.7d.1h.smile", "target": "atm", "trace_title": "7d", "color": "#_7_"}, {"qualified_name": "deribit.option.ETH.SVI.14d.1h.smile", "target": "atm", "trace_title": "14d", "color": "#_14_"}, {"qualified_name": "deribit.option.ETH.SVI.30d.1h.smile", "target": "atm", "trace_title": "30d", "color": "#_30_"}, {"qualified_name": "deribit.option.ETH.SVI.90d.1h.smile", "target": "atm", "trace_title": "90d", "color": "#_90_"}, {"qualified_name": "deribit.option.ETH.SVI.180d.1h.smile", "target": "atm", "trace_title": "180d", "color": "#_180_"}]}, "F6_BTC-Spot-Yields": {"yaxis_title": "Spot Yields", "chart_title": "BTC", "targets": [{"qualified_name": "deribit.future.BTC.7d.1h.annual.pct", "target": "pct", "trace_title": "7d", "color": "#_7_"}, {"qualified_name": "deribit.future.BTC.14d.1h.annual.pct", "target": "pct", "trace_title": "14d", "color": "#_14_"}, {"qualified_name": "deribit.future.BTC.30d.1h.annual.pct", "target": "pct", "trace_title": "30d", "color": "#_30_"}, {"qualified_name": "deribit.future.BTC.90d.1h.annual.pct", "target": "pct", "trace_title": "90d", "color": "#_90_"}, {"qualified_name": "deribit.future.BTC.180d.1h.annual.pct", "target": "pct", "trace_title": "180d", "color": "#_180_"}]}, "F7_ETH-Spot-Yields": {"yaxis_title": "Spot Yields", "chart_title": "ETH", "targets": [{"qualified_name": "deribit.future.ETH.7d.1h.annual.pct", "target": "pct", "trace_title": "7d", "color": "#_7_"}, {"qualified_name": "deribit.future.ETH.14d.1h.annual.pct", "target": "pct", "trace_title": "14d", "color": "#_14_"}, {"qualified_name": "deribit.future.ETH.30d.1h.annual.pct", "target": "pct", "trace_title": "30d", "color": "#_30_"}, {"qualified_name": "deribit.future.ETH.90d.1h.annual.pct", "target": "pct", "trace_title": "90d", "color": "#_90_"}, {"qualified_name": "deribit.future.ETH.180d.1h.annual.pct", "target": "pct", "trace_title": "180d", "color": "#_180_"}]}, "F1_Currency-Combined-Spot-Yields": {"yaxis_title": "1M Spot Yields", "chart_title": "", "targets": [{"qualified_name": "deribit.future.BTC.30d.1h.annual.pct", "target": "pct", "trace_title": "BTC", "color": "#_PERP_BTC_USD_COLOR"}, {"qualified_name": "deribit.future.ETH.30d.1h.annual.pct", "target": "pct", "trace_title": "ETH", "color": "#_PERP_ETH_USD_COLOR"}]}, "F2_Currency-Combined-1M-ATM-VOL-ETH": {"yaxis_title": "1M-ATM Implied Volatility", "chart_title": "", "targets": [{"qualified_name": "v2composite.option.BTC.SVI.30d.1h.smile", "target": "atm", "trace_title": "BTC", "color": "#_PERP_BTC_USD_COLOR"}, {"qualified_name": "v2composite.option.ETH.SVI.30d.1h.smile", "target": "atm", "trace_title": "ETH", "color": "#_PERP_ETH_USD_COLOR"}]}, "F10_Single Tenor Deribit BTC SVI PC skew": {"yaxis_title": "Skew", "chart_title": "BTC", "targets": [{"qualified_name": "deribit.option.BTC.SVI.7d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "7d", "color": "#_7_"}, {"qualified_name": "deribit.option.BTC.SVI.14d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "14d", "color": "#_14_"}, {"qualified_name": "deribit.option.BTC.SVI.30d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "30d", "color": "#_30_"}, {"qualified_name": "deribit.option.BTC.SVI.90d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "90d", "color": "#_90_"}, {"qualified_name": "deribit.option.BTC.SVI.120d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "120d", "color": "#c700ff"}, {"qualified_name": "deribit.option.BTC.SVI.180d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "180d", "color": "#_180_"}]}, "F13_Single Tenor deribit SVI PC skew": {"yaxis_title": "Skew", "chart_title": "ETH", "targets": [{"qualified_name": "deribit.option.ETH.SVI.7d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "7d", "color": "#_7_"}, {"qualified_name": "deribit.option.ETH.SVI.14d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "14d", "color": "#_14_"}, {"qualified_name": "deribit.option.ETH.SVI.30d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "30d", "color": "#_30_"}, {"qualified_name": "deribit.option.ETH.SVI.90d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "90d", "color": "#_90_"}, {"qualified_name": "deribit.option.ETH.SVI.120d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "120d", "color": "#c700ff"}, {"qualified_name": "deribit.option.ETH.SVI.180d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "180d", "color": "#_180_"}]}, "F14_Exchange_comparison_BTC_ATM_Vol": {"yaxis_title": "ATM Volatility", "chart_title": "BTC", "targets": [{"qualified_name": "okx.option.BTC.SVI.30d.1h.smile", "target": "atm", "trace_title": "OKX", "color": "#_OKX_COLOR"}, {"qualified_name": "deribit.option.BTC.SVI.30d.1h.smile", "target": "atm", "trace_title": "<PERSON><PERSON><PERSON>", "color": "#_DERIBIT_COLOR"}, {"qualified_name": "bybit.option.BTC.SVI.30d.1h.smile", "target": "atm", "trace_title": "Bybit", "color": "#_BYBIT_COLOR"}, {"qualified_name": "v2composite.option.BTC.SVI.30d.1h.smile", "target": "atm", "trace_title": "Composite", "color": "#_V2COMPOSITE_COLOR"}, {"qualified_name": "v2lyra.option.BTC.SVI.30d.1h.smile", "target": "atm", "trace_title": "Lyra", "color": "#_V2LYRA_COLOR"}]}, "F15_Exchange_comparison_ETH_ATM_Vol": {"yaxis_title": "ATM Volatility", "chart_title": "ETH", "targets": [{"qualified_name": "okx.option.ETH.SVI.30d.1h.smile", "target": "atm", "trace_title": "OKX", "color": "#_OKX_COLOR"}, {"qualified_name": "deribit.option.ETH.SVI.30d.1h.smile", "target": "atm", "trace_title": "<PERSON><PERSON><PERSON>", "color": "#_DERIBIT_COLOR"}, {"qualified_name": "bybit.option.ETH.SVI.30d.1h.smile", "target": "atm", "trace_title": "Bybit", "color": "#_BYBIT_COLOR"}, {"qualified_name": "v2composite.option.ETH.SVI.30d.1h.smile", "target": "atm", "trace_title": "Composite", "color": "#_V2COMPOSITE_COLOR"}, {"qualified_name": "v2lyra.option.ETH.SVI.30d.1h.smile", "target": "atm", "trace_title": "Lyra", "color": "#_V2LYRA_COLOR"}]}, "F16_Exchange Comparison Put-Call Skew BTC": {"yaxis_title": "Skew", "chart_title": "BTC", "targets": [{"qualified_name": "okx.option.BTC.SVI.30d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "OKX", "color": "#_OKX_COLOR"}, {"qualified_name": "deribit.option.BTC.SVI.30d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "<PERSON><PERSON><PERSON>", "color": "#_DERIBIT_COLOR"}, {"qualified_name": "bybit.option.BTC.SVI.30d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "Bybit", "color": "#_BYBIT_COLOR"}, {"qualified_name": "v2composite.option.BTC.SVI.30d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "Composite", "color": "#_V2COMPOSITE_COLOR"}, {"qualified_name": "v2lyra.option.BTC.SVI.30d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "Lyra", "color": "#_V2LYRA_COLOR"}]}, "F17_Exchange Comparison Put-Call Skew ETH": {"yaxis_title": "Skew", "chart_title": "ETH", "targets": [{"qualified_name": "okx.option.ETH.SVI.30d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "OKX", "color": "#_OKX_COLOR"}, {"qualified_name": "deribit.option.ETH.SVI.30d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "<PERSON><PERSON><PERSON>", "color": "#_DERIBIT_COLOR"}, {"qualified_name": "bybit.option.ETH.SVI.30d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "Bybit", "color": "#_BYBIT_COLOR"}, {"qualified_name": "v2composite.option.ETH.SVI.30d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "Composite", "color": "#_V2COMPOSITE_COLOR"}, {"qualified_name": "v2lyra.option.ETH.SVI.30d.1h.skew", "target": "<PERSON>del<PERSON>", "trace_title": "Lyra", "color": "#_V2LYRA_COLOR"}]}}}, "v2smiles": {"charts": {"F22_Exchange_comparison_BTC_SMILES.moneyness": {"yaxis_title": "ATM Volatility", "chart_title": "BTC -- SVI -- 30D Tenor -- _#ISO_END_HOUR_ UTC Snapshot", "targets": [{"tenor": 30, "exchange": "OKX", "currency": "BTC", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "OKX", "color": "#_OKX_COLOR"}, {"tenor": 30, "exchange": "deribit", "currency": "BTC", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "<PERSON><PERSON><PERSON>", "color": "#FF2200"}, {"tenor": 30, "exchange": "bybit", "currency": "BTC", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "Bybit", "color": "#F7A600"}, {"tenor": 30, "exchange": "v2composite", "currency": "BTC", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "Market Composite", "color": "#247CFF"}, {"tenor": 30, "exchange": "v2lyra", "currency": "BTC", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "Lyra", "color": "#25fab0"}]}, "F23_Exchange_comparison_ETH_SMILES.moneyness": {"yaxis_title": "ATM Volatility", "chart_title": "ETH -- SVI -- 30D Tenor -- _#ISO_END_HOUR_ UTC Snapshot", "targets": [{"tenor": 30, "exchange": "OKX", "currency": "ETH", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "OKX", "color": "#_OKX_COLOR"}, {"tenor": 30, "exchange": "deribit", "currency": "ETH", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "<PERSON><PERSON><PERSON>", "color": "#FF2200"}, {"tenor": 30, "exchange": "bybit", "currency": "ETH", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "Bybit", "color": "#F7A600"}, {"tenor": 30, "exchange": "v2composite", "currency": "ETH", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": " Market Composite", "color": "#247CFF"}, {"tenor": 30, "exchange": "v2lyra", "currency": "ETH", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "Lyra", "color": "#25fab0"}]}, "F18_v2composite_snap_BTC_SMILES.moneyness": {"yaxis_title": "ATM Volatility", "chart_title": "Market Composite Surface -- BTC -- SVI -- _#ISO_END_HOUR_ UTC Snapshot", "targets": [{"tenor": 7, "exchange": "v2composite", "currency": "BTC", "color_gradient": "False", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "7d", "color": "#247CFF"}, {"tenor": 14, "exchange": "v2composite", "currency": "BTC", "color_gradient": "False", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "14d", "color": "#C5C5C5"}, {"tenor": 30, "exchange": "v2composite", "currency": "BTC", "color_gradient": "False", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "30d", "color": "#FFCD00"}, {"tenor": 90, "exchange": "v2composite", "currency": "BTC", "color_gradient": "False", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "90d", "color": "#E16100"}, {"tenor": 180, "exchange": "v2composite", "currency": "BTC", "color_gradient": "False", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "180d", "color": "#43FF64"}]}, "F19_v2composite_snap_ETH_SMILES.moneyness": {"yaxis_title": "ATM Volatility", "chart_title": "Market Composite Surface -- ETH -- SVI -- _#ISO_END_HOUR_ UTC Snapshot", "targets": [{"tenor": 7, "exchange": "v2composite", "currency": "ETH", "color_gradient": "False", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "7d", "color": "#247CFF"}, {"tenor": 14, "exchange": "v2composite", "currency": "ETH", "color_gradient": "False", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "14d", "color": "#C5C5C5"}, {"tenor": 30, "exchange": "v2composite", "currency": "ETH", "color_gradient": "False", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "30d", "color": "#FFCD00"}, {"tenor": 90, "exchange": "v2composite", "currency": "ETH", "color_gradient": "False", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "90d", "color": "#E16100"}, {"tenor": 180, "exchange": "v2composite", "currency": "ETH", "color_gradient": "False", "model": "SVI", "snapshot": "_#ISO_END_", "trace_title": "180d", "color": "#43FF64"}]}}}, "aggregate_instruments": {"charts": {"BTC_28MAR25": {"yaxis_title": "Volume", "chart_title": "Bybit Tester", "targets": [{"exchange": "deribit", "instrument": "28MAR25", "type": "option", "base_asset": "BTC", "quote_asset": "BTC", "trace_title": "open_interest", "color": "#FF2200", "target_suffix": "open_interest"}, {"exchange": "deribit", "instrument": "28MAR25", "type": "option", "base_asset": "BTC", "quote_asset": "BTC", "trace_title": "volume", "color": "#FF2200", "target_suffix": "volume"}]}}}}}, "output_options": {"type": "csv", "format": "timeseries", "version": "v-00004"}}}}