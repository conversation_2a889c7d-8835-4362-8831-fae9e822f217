{"requestContext": {"stage": "dev"}, "body": {"version": "1.0.0", "type": "<PERSON><PERSON><PERSON><PERSON>", "calc": {"args": {"debug": false, "exchanges": ["bybit"], "models": ["SVI"], "currencies": ["BTC"], "frequency": {"interval": "hour", "periods": 1}, "date_range": {"absolute": {"start": "_#ISO_START_ONE_MONTH_", "end": "_#ISO_END_"}}, "plot_objects": {"v2timeseries": {"charts": {"test": {"yaxis_title": "test", "chart_title": "test", "targets": [{"qualified_name": "deribit.option.BTC.SVI.7d.1h.smile", "target": "10delta", "trace_title": "7d", "color": "#_7_"}, {"qualified_name": "deribit.option.BTC.SVI.14d.1h.smile", "target": "10delta", "trace_title": "14d", "color": "#_14_"}, {"qualified_name": "deribit.option.BTC.SVI.30d.1h.smile", "target": "10delta", "trace_title": "30d", "color": "#_30_"}, {"qualified_name": "deribit.option.BTC.SVI.90d.1h.smile", "target": "10delta", "trace_title": "90d", "color": "#_90_"}, {"qualified_name": "bybit.future.BTC_USDC.180d.1h.annual.pct", "target": "pct", "trace_title": "180d", "color": "#_180_"}]}, "Bybit BTC_USDC-Spot-Yields": {"yaxis_title": "Spot Yields", "chart_title": "BTC_USDC", "targets": [{"qualified_name": "bybit.future.BTC_USDC.7d.1h.annual.pct", "target": "pct", "trace_title": "7d", "color": "#_7_"}, {"qualified_name": "bybit.future.BTC_USDC.14d.1h.annual.pct", "target": "pct", "trace_title": "14d", "color": "#_14_"}, {"qualified_name": "bybit.future.BTC_USDC.30d.1h.annual.pct", "target": "pct", "trace_title": "30d", "color": "#_30_"}, {"qualified_name": "bybit.future.BTC_USDC.90d.1h.annual.pct", "target": "pct", "trace_title": "90d", "color": "#_90_"}, {"qualified_name": "bybit.future.BTC_USDC.180d.1h.annual.pct", "target": "pct", "trace_title": "180d", "color": "#_180_"}]}, "Bybit ETH_USDC-Spot-Yields": {"yaxis_title": "Spot Yields", "chart_title": "ETH_USDC", "targets": [{"qualified_name": "bybit.future.ETH_USDC.7d.1h.annual.pct", "target": "pct", "trace_title": "7d", "color": "#_7_"}, {"qualified_name": "bybit.future.ETH_USDC.14d.1h.annual.pct", "target": "pct", "trace_title": "14d", "color": "#_14_"}, {"qualified_name": "bybit.future.ETH_USDC.30d.1h.annual.pct", "target": "pct", "trace_title": "30d", "color": "#_30_"}, {"qualified_name": "bybit.future.ETH_USDC.90d.1h.annual.pct", "target": "pct", "trace_title": "90d", "color": "#_60_"}, {"qualified_name": "bybit.future.ETH_USDC.180d.1h.annual.pct", "target": "pct", "trace_title": "180d", "color": "#_180_"}]}}}, "instruments_aggregator": {"charts": {"Bybit Perpetuals": {"yaxis_title": "OI", "chart_title": "Bybit Perpetuals", "aggregation_level": "currency", "asset_type": "perpetual", "currencies": ["ETH", "BTC"], "filters": {"exchanges": [{"exchange": "bybit", "original_quote_asset": "USDC"}, {"exchange": "bybit", "original_quote_asset": "USDT"}]}, "target_suffix": "open_interest", "chart_formats": {"dollar_denomination": true, "chart_type": "stacked_area"}, "resample_frequency": "hour"}, "Bybit Futures Open Interest": {"yaxis_title": "OI", "chart_title": "Bybit Futures Open Interest", "aggregation_level": "currency", "sub_aggregation_level": "expiry", "asset_type": "future", "currencies": ["BTC", "ETH"], "filters": {"exchanges": [{"exchange": "bybit", "original_quote_asset": "USDC"}]}, "target_suffix": "open_interest", "chart_formats": {"show_full_legend": false, "dollar_denomination": true, "chart_type": "stacked_area"}, "resample_frequency": "hour"}, "Bybit Futures Volumes": {"yaxis_title": "Volume", "chart_title": "Bybit Futures Volumes", "aggregation_level": "currency", "sub_aggregation_level": "expiry", "asset_type": "future", "currencies": ["BTC", "ETH"], "filters": {"exchanges": [{"exchange": "bybit", "original_quote_asset": "USDC"}]}, "target_suffix": "volume", "chart_formats": {"show_full_legend": false, "dollar_denomination": true, "chart_type": "bar"}, "resample_frequency": "day"}, "Bybit USDT Perpetuals Volumes": {"yaxis_title": "Volume", "chart_title": "Bybit USDT Perpetual Volumes", "aggregation_level": "currency", "asset_type": "perpetual", "currencies": ["BTC", "ETH"], "filters": {"exchanges": [{"exchange": "bybit", "original_quote_asset": "USDT"}, {"exchange": "bybit", "original_quote_asset": "USDC"}]}, "target_suffix": "volume", "chart_formats": {"dollar_denomination": true, "chart_type": "bar"}, "resample_frequency": "day"}, "Bybit BTC Options Volumes": {"yaxis_title": "Volume", "chart_title": "Bybit BTC Option Volumes", "aggregation_level": "option_type", "sub_aggregation_level": "expiry", "asset_type": "option", "currencies": ["BTC"], "filters": {"exchanges": [{"exchange": "bybit", "original_quote_asset": "USDC"}], "currencies": ["BTC"]}, "target_suffix": "volume", "chart_formats": {"dollar_denomination": true, "chart_type": "bar", "invert_aggregation_levels": ["P"], "show_full_legend": false}, "resample_frequency": "day"}, "Bybit ETH Options Volumes": {"yaxis_title": "Volume", "chart_title": "Bybit ETH Option Volumes", "aggregation_level": "option_type", "sub_aggregation_level": "expiry", "asset_type": "option", "currencies": ["ETH"], "filters": {"exchanges": [{"exchange": "bybit", "original_quote_asset": "USDC"}], "currencies": ["ETH"]}, "target_suffix": "volume", "chart_formats": {"dollar_denomination": true, "chart_type": "bar", "invert_aggregation_levels": ["P"], "show_full_legend": false}, "resample_frequency": "day"}, "Bybit ETH Futures OI": {"yaxis_title": "OI", "chart_title": "Bybit ETH Futures OI", "aggregation_level": "expiry", "asset_type": "future", "currencies": ["ETH"], "filters": {"exchanges": [{"exchange": "bybit", "original_quote_asset": "USDC"}], "currencies": ["ETH"]}, "target_suffix": "open_interest", "chart_formats": {"dollar_denomination": true, "chart_type": "stacked_area"}, "resample_frequency": "hour"}, "Bybit BTC Futures OI": {"yaxis_title": "OI", "chart_title": "Bybit BTC Futures OI", "aggregation_level": "expiry", "asset_type": "future", "currencies": ["BTC"], "filters": {"exchanges": [{"exchange": "bybit", "original_quote_asset": "USDC"}], "currencies": ["BTC"]}, "target_suffix": "open_interest", "chart_formats": {"dollar_denomination": true, "chart_type": "stacked_area"}, "resample_frequency": "hour"}, "Bybit ETH Options OI": {"yaxis_title": "OI", "chart_title": "Bybit ETH Options OI", "aggregation_level": "option_type", "sub_aggregation_level": "expiry", "asset_type": "option", "currencies": ["ETH"], "filters": {"exchanges": [{"exchange": "bybit", "original_quote_asset": "USDC"}], "currencies": ["ETH"]}, "target_suffix": "open_interest", "chart_formats": {"dollar_denomination": true, "chart_type": "stacked_area", "invert_aggregation_levels": ["P"], "show_full_legend": false}, "resample_frequency": "hour"}, "Bybit BTC Options OI": {"yaxis_title": "OI", "chart_title": "Bybit BTC Options OI", "aggregation_level": "option_type", "sub_aggregation_level": "expiry", "asset_type": "option", "currencies": ["BTC"], "filters": {"exchanges": [{"exchange": "bybit", "original_quote_asset": "USDC"}], "currencies": ["BTC"]}, "target_suffix": "open_interest", "chart_formats": {"dollar_denomination": true, "chart_type": "stacked_area", "invert_aggregation_levels": ["P"], "show_full_legend": false}, "resample_frequency": "hour"}}}}}, "output_options": {"type": "csv", "format": "timeseries", "version": ""}}}}