{"requestContext": {"stage": "dev"}, "body": {"version": "1.0.0", "type": "<PERSON><PERSON><PERSON><PERSON>", "calc": {"args": {"debug": false, "exchanges": ["okx"], "models": ["SVI"], "currencies": ["BTC"], "frequency": {"interval": "hour", "periods": 1}, "date_range": {"absolute": {"start": "_#ISO_START_ONE_MONTH_", "end": "_#ISO_END_"}}, "plot_objects": {"instruments_aggregator": {"charts": {"okx Perpetuals": {"yaxis_title": "OI", "chart_title": "okx Perpetuals", "aggregation_level": "currency", "asset_type": "perpetual", "currencies": ["ETH", "BTC"], "filters": {"exchanges": [{"exchange": "okx", "original_quote_asset": "USDT"}]}, "target_suffix": "open_interest", "chart_formats": {"dollar_denomination": true, "chart_type": "stacked_area"}, "resample_frequency": "hour"}}}}}, "output_options": {"type": "csv", "format": "timeseries", "version": ""}}}}