{"body": {"version": "1.0.0", "type": "ResearchVolDashboard", "calc": {"args": {"debug": false, "exchanges": ["v2composite"], "models": ["SVI"], "currencies": [""], "frequency": {"interval": "hour", "periods": 1}, "date_range": {"absolute": {"start": "2025-07-09T14:00:00Z", "end": "2025-08-08T14:00:00Z"}}, "plot_objects": {"v2timeseries": {"charts": {"BTC Call Wing Spread Signal": {"yaxis_title": "Butterfly Spread", "chart_title": "BTC Call Wing Spread Signal", "additional_lookback_days": 15, "include_latest_datapoints": true, "calculation": "WING_SPREAD_CALC", "targets": [{"qualified_name": "v2composite.option.BTC.SVI.7d.1h.smile", "trace_title": "7d", "color": "#247CFF", "target": "10delta"}, {"qualified_name": "v2composite.option.BTC.SVI.7d.1h.smile", "target": "atm"}, {"qualified_name": "v2composite.option.BTC.SVI.30d.1h.smile", "trace_title": "30d", "color": "#FFCD00", "target": "10delta"}, {"qualified_name": "v2composite.option.BTC.SVI.30d.1h.smile", "target": "atm"}, {"qualified_name": "v2composite.option.BTC.SVI.60d.1h.smile", "trace_title": "60d", "color": "#FF54AF", "target": "10delta"}, {"qualified_name": "v2composite.option.BTC.SVI.60d.1h.smile", "target": "atm"}, {"qualified_name": "v2composite.option.BTC.SVI.90d.1h.smile", "trace_title": "90d", "color": "#E16100", "target": "10delta"}, {"qualified_name": "v2composite.option.BTC.SVI.90d.1h.smile", "target": "atm"}, {"qualified_name": "v2composite.option.BTC.SVI.180d.1h.smile", "trace_title": "180d", "color": "#43FF64", "target": "10delta"}, {"qualified_name": "v2composite.option.BTC.SVI.180d.1h.smile", "target": "atm"}]}, "ETH Call Wing Spread Signal": {"yaxis_title": "Butterfly Spread", "chart_title": "ETH Call Wing Spread Signal", "additional_lookback_days": 15, "calculation": "WING_SPREAD_CALC", "targets": [{"qualified_name": "v2composite.option.ETH.SVI.7d.1h.smile", "trace_title": "7d", "color": "#247CFF", "target": "10delta"}, {"qualified_name": "v2composite.option.ETH.SVI.7d.1h.smile", "target": "atm"}, {"qualified_name": "v2composite.option.ETH.SVI.30d.1h.smile", "trace_title": "30d", "color": "#FFCD00", "target": "10delta"}, {"qualified_name": "v2composite.option.ETH.SVI.30d.1h.smile", "target": "atm"}, {"qualified_name": "v2composite.option.ETH.SVI.60d.1h.smile", "trace_title": "60d", "color": "#FF54AF", "target": "10delta"}, {"qualified_name": "v2composite.option.ETH.SVI.60d.1h.smile", "target": "atm"}, {"qualified_name": "v2composite.option.ETH.SVI.90d.1h.smile", "trace_title": "90d", "color": "#E16100", "target": "10delta"}, {"qualified_name": "v2composite.option.ETH.SVI.90d.1h.smile", "target": "atm"}, {"qualified_name": "v2composite.option.ETH.SVI.180d.1h.smile", "trace_title": "180d", "color": "#43FF64", "target": "10delta"}, {"qualified_name": "v2composite.option.ETH.SVI.180d.1h.smile", "target": "atm"}]}, "BTC Put Wing Spread Signal": {"yaxis_title": "Butterfly Spread", "chart_title": "BTC Put Wing Spread Signal", "additional_lookback_days": 15, "calculation": "WING_SPREAD_CALC", "targets": [{"qualified_name": "v2composite.option.BTC.SVI.7d.1h.smile", "trace_title": "7d", "color": "#247CFF", "target": "-<PERSON><PERSON><PERSON>"}, {"qualified_name": "v2composite.option.BTC.SVI.7d.1h.smile", "target": "atm"}, {"qualified_name": "v2composite.option.BTC.SVI.30d.1h.smile", "trace_title": "30d", "color": "#FFCD00", "target": "-<PERSON><PERSON><PERSON>"}, {"qualified_name": "v2composite.option.BTC.SVI.30d.1h.smile", "target": "atm"}, {"qualified_name": "v2composite.option.BTC.SVI.60d.1h.smile", "trace_title": "60d", "color": "#FF54AF", "target": "-<PERSON><PERSON><PERSON>"}, {"qualified_name": "v2composite.option.BTC.SVI.60d.1h.smile", "target": "atm"}, {"qualified_name": "v2composite.option.BTC.SVI.90d.1h.smile", "trace_title": "90d", "color": "#E16100", "target": "-<PERSON><PERSON><PERSON>"}, {"qualified_name": "v2composite.option.BTC.SVI.90d.1h.smile", "target": "atm"}, {"qualified_name": "v2composite.option.BTC.SVI.180d.1h.smile", "trace_title": "180d", "color": "#43FF64", "target": "-<PERSON><PERSON><PERSON>"}, {"qualified_name": "v2composite.option.BTC.SVI.180d.1h.smile", "target": "atm"}]}, "ETH Put Wing Spread Signal": {"yaxis_title": "Butterfly Spread", "chart_title": "ETH Put Wing Spread Signal", "additional_lookback_days": 15, "calculation": "WING_SPREAD_CALC", "targets": [{"qualified_name": "v2composite.option.ETH.SVI.7d.1h.smile", "trace_title": "7d", "color": "#247CFF", "target": "-<PERSON><PERSON><PERSON>"}, {"qualified_name": "v2composite.option.ETH.SVI.7d.1h.smile", "target": "atm"}, {"qualified_name": "v2composite.option.ETH.SVI.30d.1h.smile", "trace_title": "30d", "color": "#FFCD00", "target": "-<PERSON><PERSON><PERSON>"}, {"qualified_name": "v2composite.option.ETH.SVI.30d.1h.smile", "target": "atm"}, {"qualified_name": "v2composite.option.ETH.SVI.60d.1h.smile", "trace_title": "60d", "color": "#FF54AF", "target": "-<PERSON><PERSON><PERSON>"}, {"qualified_name": "v2composite.option.ETH.SVI.60d.1h.smile", "target": "atm"}, {"qualified_name": "v2composite.option.ETH.SVI.90d.1h.smile", "trace_title": "90d", "color": "#E16100", "target": "-<PERSON><PERSON><PERSON>"}, {"qualified_name": "v2composite.option.ETH.SVI.90d.1h.smile", "target": "atm"}, {"qualified_name": "v2composite.option.ETH.SVI.180d.1h.smile", "trace_title": "180d", "color": "#43FF64", "target": "-<PERSON><PERSON><PERSON>"}, {"qualified_name": "v2composite.option.ETH.SVI.180d.1h.smile", "target": "atm"}]}}}}}, "output_options": {"format": "timeseries", "version": "v-00004"}}}, "requestContext": {"stage": ""}}