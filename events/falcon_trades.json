{"body": {"version": "1.0.0", "type": "ResearchVolDashboard", "calc": {"args": {"debug": false, "exchanges": ["deribit"], "models": ["SABR"], "currencies": ["ETH", "BTC"], "frequency": {"interval": "hour", "periods": 1}, "date_range": {"absolute": {"start": "_#ISO_START_ONE_MONTH_", "end": "_#ISO_END_"}}, "plot_objects": {"inversion_monitor": {"series_to_plot": "atm", "tenors": [7, 14, 30, 90, 180], "model": "SABR", "exchange": "deribit"}, "v2timeseries": {"charts": {"F9_deribit BTC SVI ATM volatility": {"yaxis_title": "ATM Volatility", "chart_title": "BTC", "targets": [{"qualified_name": "deribit.option.BTC.SVI.7d.1h.smile", "target": "atm", "trace_title": "7d", "color": "#_7_"}, {"qualified_name": "deribit.option.BTC.SVI.14d.1h.smile", "target": "atm", "trace_title": "14d", "color": "#_14_"}, {"qualified_name": "deribit.option.BTC.SVI.30d.1h.smile", "target": "atm", "trace_title": "30d", "color": "#_30_"}, {"qualified_name": "deribit.option.BTC.SVI.90d.1h.smile", "target": "atm", "trace_title": "90d", "color": "#_90_"}, {"qualified_name": "deribit.option.BTC.SVI.180d.1h.smile", "target": "atm", "trace_title": "180d", "color": "#_180_"}]}, "BTC Call Wing Spreads": {"yaxis_title": "Butterfly Spread", "chart_title": "Call Spread BTC 30D", "calculation": "WING_SPREAD_CALC", "additional_lookback_days": 50, "targets": [{"qualified_name": "deribit.option.BTC.SVI.7d.1h.smile", "target": "10delta", "trace_title": "7d", "color": "#_7_"}, {"qualified_name": "deribit.option.BTC.SVI.7d.1h.smile", "target": "atm"}, {"qualified_name": "deribit.option.BTC.SVI.30d.1h.smile", "target": "10delta"}, {"qualified_name": "deribit.option.BTC.SVI.30d.1h.smile", "target": "atm", "trace_title": "30d", "color": "#_30_"}, {"qualified_name": "deribit.option.BTC.SVI.60d.1h.smile", "target": "10delta"}, {"qualified_name": "deribit.option.BTC.SVI.60d.1h.smile", "target": "atm", "trace_title": "60d", "color": "#_60_"}, {"qualified_name": "deribit.option.BTC.SVI.90d.1h.smile", "target": "10delta"}, {"qualified_name": "deribit.option.BTC.SVI.90d.1h.smile", "target": "atm", "trace_title": "90d", "color": "#_90_"}, {"qualified_name": "deribit.option.BTC.SVI.180d.1h.smile", "target": "10delta"}, {"qualified_name": "deribit.option.BTC.SVI.180d.1h.smile", "target": "atm", "trace_title": "180d", "color": "#_180_"}]}, "BTC Put Wing Spreads": {"yaxis_title": "Butterfly Spread", "chart_title": "Put Spread BTC 30D", "calculation": "WING_SPREAD_CALC", "additional_lookback_days": 50, "targets": [{"qualified_name": "deribit.option.BTC.SVI.7d.1h.smile", "target": "-<PERSON><PERSON><PERSON>", "trace_title": "7d", "color": "#_7_"}, {"qualified_name": "deribit.option.BTC.SVI.7d.1h.smile", "target": "atm"}, {"qualified_name": "deribit.option.BTC.SVI.30d.1h.smile", "target": "-<PERSON><PERSON><PERSON>"}, {"qualified_name": "deribit.option.BTC.SVI.30d.1h.smile", "target": "atm", "trace_title": "30d", "color": "#_30_"}, {"qualified_name": "deribit.option.BTC.SVI.60d.1h.smile", "target": "-<PERSON><PERSON><PERSON>"}, {"qualified_name": "deribit.option.BTC.SVI.60d.1h.smile", "target": "atm", "trace_title": "60d", "color": "#_60_"}, {"qualified_name": "deribit.option.BTC.SVI.90d.1h.smile", "target": "-<PERSON><PERSON><PERSON>"}, {"qualified_name": "deribit.option.BTC.SVI.90d.1h.smile", "target": "atm", "trace_title": "90d", "color": "#_90_"}, {"qualified_name": "deribit.option.BTC.SVI.180d.1h.smile", "target": "-<PERSON><PERSON><PERSON>"}, {"qualified_name": "deribit.option.BTC.SVI.180d.1h.smile", "target": "atm", "trace_title": "180d", "color": "#_180_"}]}, "ETH Call Wing Spreads": {"yaxis_title": "Butterfly Spread", "chart_title": "Call Spread ETH 30D", "calculation": "WING_SPREAD_CALC", "additional_lookback_days": 50, "targets": [{"qualified_name": "deribit.option.ETH.SVI.7d.1h.smile", "target": "10delta", "trace_title": "7d", "color": "#_7_"}, {"qualified_name": "deribit.option.ETH.SVI.7d.1h.smile", "target": "atm"}, {"qualified_name": "deribit.option.ETH.SVI.30d.1h.smile", "target": "10delta"}, {"qualified_name": "deribit.option.ETH.SVI.30d.1h.smile", "target": "atm", "trace_title": "30d", "color": "#_30_"}, {"qualified_name": "deribit.option.ETH.SVI.60d.1h.smile", "target": "10delta"}, {"qualified_name": "deribit.option.ETH.SVI.60d.1h.smile", "target": "atm", "trace_title": "60d", "color": "#_60_"}, {"qualified_name": "deribit.option.ETH.SVI.90d.1h.smile", "target": "10delta"}, {"qualified_name": "deribit.option.ETH.SVI.90d.1h.smile", "target": "atm", "trace_title": "90d", "color": "#_90_"}, {"qualified_name": "deribit.option.ETH.SVI.180d.1h.smile", "target": "10delta"}, {"qualified_name": "deribit.option.ETH.SVI.180d.1h.smile", "target": "atm", "trace_title": "180d", "color": "#_180_"}]}, "ETH Put Wing Spreads": {"yaxis_title": "Butterfly Spread", "chart_title": "Put Spread ETH 30D", "calculation": "WING_SPREAD_CALC", "additional_lookback_days": 50, "targets": [{"qualified_name": "deribit.option.ETH.SVI.7d.1h.smile", "target": "-<PERSON><PERSON><PERSON>", "trace_title": "7d", "color": "#_7_"}, {"qualified_name": "deribit.option.ETH.SVI.7d.1h.smile", "target": "atm"}, {"qualified_name": "deribit.option.ETH.SVI.30d.1h.smile", "target": "-<PERSON><PERSON><PERSON>"}, {"qualified_name": "deribit.option.ETH.SVI.30d.1h.smile", "target": "atm", "trace_title": "30d", "color": "#_30_"}, {"qualified_name": "deribit.option.ETH.SVI.60d.1h.smile", "target": "-<PERSON><PERSON><PERSON>"}, {"qualified_name": "deribit.option.ETH.SVI.60d.1h.smile", "target": "atm", "trace_title": "60d", "color": "#_60_"}, {"qualified_name": "deribit.option.ETH.SVI.90d.1h.smile", "target": "-<PERSON><PERSON><PERSON>"}, {"qualified_name": "deribit.option.ETH.SVI.90d.1h.smile", "target": "atm", "trace_title": "90d", "color": "#_90_"}, {"qualified_name": "deribit.option.ETH.SVI.180d.1h.smile", "target": "-<PERSON><PERSON><PERSON>"}, {"qualified_name": "deribit.option.ETH.SVI.180d.1h.smile", "target": "atm", "trace_title": "180d", "color": "#_180_"}]}}}, "v2smiles": {"charts": {}}, "aggregate_instruments": {"charts": {"BTC_28MAR25": {"yaxis_title": "Volume", "chart_title": "Bybit Tester", "targets": [{"exchange": "deribit", "instrument": "28MAR25", "type": "option", "base_asset": "BTC", "quote_asset": "BTC", "trace_title": "open_interest", "color": "#FF2200", "target_suffix": "open_interest"}, {"exchange": "deribit", "instrument": "28MAR25", "type": "option", "base_asset": "BTC", "quote_asset": "BTC", "trace_title": "volume", "color": "#FF2200", "target_suffix": "volume"}]}}}}}, "output_options": {"type": "csv", "format": "timeseries", "version": "v-00004"}}}}