"""
Example demonstrating how to use horizontal reference lines in make_flex_timeseries_fig.

This example shows how to add horizontal reference lines to timeseries charts,
including different styling options and how the y-axis range is automatically
adjusted to ensure the lines are visible.
"""

import os
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd

from research_vol_dashboard.sandbox_plots_utils import make_flex_timeseries_fig


def create_sample_data() -> pd.DataFrame:
    """Create sample timeseries data for demonstration."""
    timestamps = pd.date_range("2024-01-01", periods=20, freq="h")
    data = []

    for i, ts in enumerate(timestamps):
        # Create some sample volatility data with variation
        base_vol = 0.5 + 0.1 * (i / 20)  # Trending upward
        noise = 0.02 * (i % 3 - 1)  # Some noise

        data.append(
            {
                "timestamp": ts,
                "qualified_name": "example.option.BTC.SVI.7d.1h.smile",
                "atm": base_vol + noise,
                "10delta": base_vol + 0.05 + noise,
                "25delta": base_vol + 0.03 + noise,
            }
        )

    df = pd.DataFrame(data)
    df.set_index("timestamp", inplace=True)
    return df


def example_basic_horizontal_lines():
    """Example 1: Basic horizontal lines with different styling."""
    print("Creating example with basic horizontal lines...")

    data_df = create_sample_data()

    targets = [
        {
            "qualified_name": "example.option.BTC.SVI.7d.1h.smile",
            "target": "atm",
            "trace_title": "ATM Vol",
            "color": "#247CFF",
        },
        {
            "qualified_name": "example.option.BTC.SVI.7d.1h.smile",
            "target": "10delta",
            "trace_title": "10 Delta Vol",
            "color": "#FFCD00",
        },
    ]

    # Define horizontal reference lines
    horizontal_lines = [
        {
            "y": 0.55,
            "color": "rgba(255, 0, 0, 0.7)",  # Semi-transparent red
            "line_width": 2,
            "line_dash": "dash",
            "label": "Target Level",
        },
        {
            "y": 0.65,
            "color": "rgba(0, 255, 0, 0.7)",  # Semi-transparent green
            "line_width": 1,
            "line_dash": "solid",
            "label": "Upper Bound",
        },
        {
            "y": 0.45,
            "color": "rgba(255, 165, 0, 0.7)",  # Semi-transparent orange
            "line_width": 1,
            "line_dash": "dot",
            "label": "Lower Bound",
        },
    ]

    fig = make_flex_timeseries_fig(
        data_df=data_df,
        target_to_plot=targets,
        chart_name="example_basic_hlines",
        yaxis_title="Implied Volatility",
        chart_type="timeseries",
        chart_title="Volatility with Reference Lines",
        tickprefix=None,
        ticksuffix="%",
        horizontal_lines=horizontal_lines,
    )

    print(f"Created chart with {len(horizontal_lines)} horizontal lines")
    print("Lines will be visible even if they extend beyond the data range")
    return fig


def example_extended_range_lines():
    """Example 2: Horizontal lines that extend beyond the data range."""
    print("Creating example with extended range horizontal lines...")

    data_df = create_sample_data()

    targets = [
        {
            "qualified_name": "example.option.BTC.SVI.7d.1h.smile",
            "target": "atm",
            "trace_title": "ATM Vol",
            "color": "#247CFF",
        },
    ]

    # Add lines that extend well beyond the data range
    # Data ranges approximately from 0.48 to 0.62
    horizontal_lines = [
        {
            "y": 0.3,  # Well below data range
            "color": "red",
            "line_width": 2,
            "label": "Support Level",
        },
        {
            "y": 0.8,  # Well above data range
            "color": "green",
            "line_width": 2,
            "label": "Resistance Level",
        },
    ]

    fig = make_flex_timeseries_fig(
        data_df=data_df,
        target_to_plot=targets,
        chart_name="example_extended_range",
        yaxis_title="Implied Volatility",
        chart_type="timeseries",
        chart_title="Extended Range with Reference Lines",
        tickprefix=None,
        ticksuffix="%",
        horizontal_lines=horizontal_lines,
    )

    print("Y-axis range automatically expanded to include all horizontal lines")
    return fig


def example_minimal_lines():
    """Example 3: Minimal horizontal lines without labels."""
    print("Creating example with minimal horizontal lines...")

    data_df = create_sample_data()

    targets = [
        {
            "qualified_name": "example.option.BTC.SVI.7d.1h.smile",
            "target": "25delta",
            "trace_title": "25 Delta Vol",
            "color": "#FF6B6B",
        },
    ]

    # Simple horizontal lines with minimal styling
    horizontal_lines = [
        {"y": 0.52},  # Uses default styling
        {"y": 0.58, "color": "blue"},  # Custom color only
    ]

    fig = make_flex_timeseries_fig(
        data_df=data_df,
        target_to_plot=targets,
        chart_name="example_minimal",
        yaxis_title="Implied Volatility",
        chart_type="timeseries",
        chart_title="Minimal Reference Lines",
        tickprefix=None,
        ticksuffix="%",
        horizontal_lines=horizontal_lines,
    )

    print("Created chart with minimal horizontal line styling")
    return fig


if __name__ == "__main__":
    print("Horizontal Lines Examples")
    print("=" * 50)

    # Run examples
    fig1 = example_basic_horizontal_lines()
    print()

    fig2 = example_extended_range_lines()
    print()

    fig3 = example_minimal_lines()
    print()

    print("Examples completed successfully!")
    print("\nHorizontal line parameters:")
    print("- y: Y-axis value for the line (required)")
    print("- color: Line color (default: 'rgba(255, 255, 255, 0.5)')")
    print("- line_width: Line thickness (default: 1)")
    print(
        "- line_dash: Line style - 'solid', 'dash', 'dot', etc. (default: 'solid')"
    )
    print("- label: Text label for the line (optional)")
