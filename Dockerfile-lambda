# syntax=docker/dockerfile:1.7

ARG PYTHON_TAG=3.12

FROM ghcr.io/astral-sh/uv:0.8.3 AS uv

FROM public.ecr.aws/lambda/python:${PYTHON_TAG} AS builder
SHELL ["/bin/bash", "-eux", "-o", "pipefail", "-c"]

# Work inside the Lambda task root so file mounts land where uv expects them
WORKDIR ${LAMBDA_TASK_ROOT}

# Improve cold starts and determinism; support bind-mount caching
ENV UV_COMPILE_BYTECODE=1 \
    UV_NO_INSTALLER_METADATA=1 \
    UV_LINK_MODE=copy \
    GIT_TERMINAL_PROMPT=0

# Optional toolchain if you build native wheels
RUN dnf install -y --nodocs gcc git && dnf clean all

RUN --mount=from=uv,source=/uv,target=/bin/uv \
    --mount=type=cache,target=/root/.cache/uv \
    --mount=type=bind,source=uv.lock,target=uv.lock \
    --mount=type=bind,source=pyproject.toml,target=pyproject.toml \
    --mount=type=secret,id=GITHUB_TOKEN \
    if [ -f /run/secrets/GITHUB_TOKEN ]; then \
      set +x; T="$(cat /run/secrets/GITHUB_TOKEN)"; set -x; \
      export GIT_CONFIG_COUNT=1 \
      export GIT_CONFIG_KEY_0="url.https://x-access-token:${T}@github.com/.insteadOf" \
      export GIT_CONFIG_VALUE_0="https://github.com/"; \
    fi; \
    uv export --frozen --no-emit-workspace --no-dev --no-editable -o requirements.txt && \
    uv pip install --no-deps -r requirements.txt --target "${LAMBDA_TASK_ROOT}"


FROM public.ecr.aws/lambda/python:${PYTHON_TAG}

# - Plotly's fig.to_image() uses Kaleido, a native binary spawned at runtime.
# - Kaleido depends on libexpat (libexpat.so.1). In our previous single-stage Dockerfile,
#   installing 'git' pulled 'expat' as a transitive dependency, so the lib was present.
# - With the new multi-stage build, the final image is "clean" (no git), so libexpat
#   is missing and Kaleido fails with:
#     ./bin/kaleido: error while loading shared libraries: libexpat.so.1: No such file
# - Installing 'expat' HERE ensures the runtime image contains libexpat.so.1.
RUN dnf install -y --nodocs expat && dnf clean all

COPY --from=builder ${LAMBDA_TASK_ROOT} ${LAMBDA_TASK_ROOT}


COPY . ${LAMBDA_TASK_ROOT}
CMD ["app.lambda_handler"]
