import argparse
import json
import logging
import sys
from datetime import UTC, datetime, timedelta
from typing import Any

import pandas as pd
import utils_general as util
from datagrabber import GrabP<PERSON><PERSON>, grab, make_timeseries_query

# from datagrabber import grab
# from datagrabber_queries import make_timeseries_query
# from datagrabber_types import *

util.setup_python_logger(logging.INFO)

parser = argparse.ArgumentParser(
    formatter_class=argparse.ArgumentDefaultsHelpFormatter
)
parser.add_argument(
    "-query_cardinality",
    type=str.lower,
    default="single",
    help="Whether to construct multiple queries based on timestamp, in order to test for runtime impact of the number of queries: single / multiple",
)
parser.add_argument(
    "-print_results",
    default="",
    help="Where to print results: stdout / <file_name.json>",
)
args = parser.parse_args()


def main() -> None:
    start, end = (
        util.from_iso("2020-01-01T00:00:00.000+00:00").timestamp() * 1e9,
        util.from_iso(str(pd.Timestamp.now())).timestamp() * 1e9,
        # util.from_iso("2024-01-30T23:00:00.000+00:00").timestamp() * 1e9,
    )
    queries: list[GrabParams] = []
    qfns = [
        # f"deribit.option.BTC.SABR.{tenor}d.1h.smile"
        "deribit.spot.BTCUSD.1h.index.px",
        # "v-00002.deribit.option.BTC.SVI.30d.1h.smile",
        "v-00004.deribit.option.BTC.SVI.30d.1h.smile",
        "deribit.future.BTC.30d.1h.curve",
        "deribit.perpetual.BTC-PERPETUAL.1h.funding.rate",
        "deribit.spot.ETHUSD.1h.index.px",
        # "v-00002.deribit.option.ETH.SVI.30d.1h.smile",
        "v-00004.deribit.option.ETH.SVI.30d.1h.smile",
        "deribit.future.ETH.30d.1h.curve",
        "deribit.perpetual.ETH-PERPETUAL.1h.funding.rate",
    ]
    # fields = ["timestamp", "qualified_name", "atm", "tenor_days"]
    fields = [
        "timestamp",
        "qualified_name",
        "px",
        "25delta",
        "-25delta",
        "rate",
        "future",
    ]
    if args.query_cardinality == "multiple":
        while start < end:
            queries.append(make_timeseries_query(qfns, start, end, fields))
            start = start + timedelta(days=1)
    else:
        queries.append(
            make_timeseries_query(
                qfns,
                start,
                end,
                fields,
            )
        )

    clock_start = datetime.now(tz=UTC).timestamp()
    results: Any = []
    try:
        results = grab(queries)
    except Exception:
        logging.exception("Error on grab.")

    clock_end = datetime.now(tz=UTC).timestamp()
    logging.info(
        f"Grabbing {len(queries)} queries took {round(clock_end - clock_start, 2)}s"
    )
    # if args.print_results == "stdout":
    #     logging.info(f"results={results}")
    # elif args.print_results:
    file_path = (
        "/Users/<USER>/Documents/Work/BlockScholes/researchVolDashboard/scripts/tester.json"
        # f"{os.path.dirname(os.path.realpath(__file__))}/{args.print_results}"
    )
    logging.info(f"Writing results to {file_path}")
    print(
        json.dumps(results, indent=4),
        file=open(
            file_path,
            "w",
        ),
    )


if __name__ == "__main__":

    sys.exit(main())
