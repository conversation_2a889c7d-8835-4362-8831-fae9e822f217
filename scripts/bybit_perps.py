import asyncio
import logging
from datetime import UTC, datetime
from pathlib import Path
from typing import Any

import aiohttp
import nest_asyncio
import pandas as pd

from research_vol_dashboard.constants import BYBIT_PERP_INSTRUMENTS, FR_API_URL

nest_asyncio.apply()


def build_params(
    symbol,
    category: str,
    start_time=None,
    end_time=None,
    limit=200,
    cursor=None,
    interval_time="1h",
) -> dict[str, str | None | int]:
    params = {
        "category": category,
        "symbol": symbol,
        "startTime": start_time,
        "endTime": end_time,
        "intervalTime": interval_time,
        "limit": limit,
        "cursor": cursor,
    }
    # Filter out None values
    return {k: v for k, v in params.items() if v is not None}


async def fetch_funding_rate(
    session,
    symbol: str,
    category: str,
    start_time=None,
    end_time=None,
    limit=200,
    cursor=None,
    interval_time="1h",
):
    params = build_params(
        symbol=symbol,
        start_time=start_time,
        end_time=end_time,
        limit=limit,
        cursor=cursor,
        interval_time=interval_time,
        category=category,
    )
    async with session.get(FR_API_URL, params=params) as response:
        if response.status == 200:
            data = await response.json()
            return data
        else:
            return None


async def fetch_all_data_for_symbol(
    session,
    symbol,
    start_time,
    end_time,
    category,
    limit=200,
    interval_time="1h",
):
    all_results = []
    current_cursor = None
    while True:
        result = await fetch_funding_rate(
            session=session,
            symbol=symbol,
            start_time=start_time,
            end_time=end_time,
            limit=limit,
            category=category,
            cursor=current_cursor,
            interval_time=interval_time,
        )
        if result and not result["retExtInfo"]:
            results_list = result["result"]["list"]
            all_results.extend(results_list)
            # Check if we need to fetch the next page
            if (
                len(results_list) < limit
                or "nextPageCursor" not in result["result"]
            ):
                break
            current_cursor = result["result"]["nextPageCursor"]
        else:
            break
    return {symbol: all_results}


async def fetch_all_symbols(
    symbols,
    start_time,
    end_time,
    category,
    limit=200,
    interval_time="1h",
) -> dict:
    async with aiohttp.ClientSession() as session:
        tasks = []
        for symbol in symbols:
            task = fetch_all_data_for_symbol(
                session=session,
                symbol=symbol,
                start_time=start_time,
                end_time=end_time,
                limit=limit,
                interval_time=interval_time,
                category=category,
            )
            tasks.append(task)
        results = await asyncio.gather(*tasks)
        # Flatten the list of dictionaries into a single dictionary
        grouped_results = {}
        for result in results:
            for symbol, data in result.items():
                if symbol in grouped_results:
                    grouped_results[symbol].extend(data)
                else:
                    grouped_results[symbol] = data
        return grouped_results


def main() -> None:
    start_time = int(
        datetime(
            year=2024,
            month=5,
            day=24,
            hour=8,
            minute=0,
            second=0,
            tzinfo=UTC,
        ).timestamp()
        * 1000
    )
    end_time = int(
        datetime(
            year=2024,
            month=7,
            day=4,
            hour=8,
            minute=0,
            second=0,
            tzinfo=UTC,
        ).timestamp()
        * 1000
    )
    limit = 200
    interval_time = "1h"

    options = asyncio.run(
        fetch_all_symbols(
            symbols=BYBIT_PERP_INSTRUMENTS,
            start_time=start_time,
            end_time=end_time,
            limit=limit,
            interval_time=interval_time,
            category="linear",
        )
    )

    dfs = []
    for symbol, data_points in options.items():
        logging.info(f"Data for symbol: {symbol}")

        symbol_df = pd.DataFrame(data_points).rename(
            columns={
                "openInterestTimestamp": "timestamp",
                "openInterest": "oi",
                "fundingRate": "rate",
                "fundingRateTimestamp": "timestamp",
            }
        )
        symbol_df["qualified_name"] = (
            f"bybit.perpetual.{symbol}.{interval_time}.funding.rate"
        )
        dfs.append(symbol_df)

    all_dfs = pd.concat(dfs)
    all_dfs.to_csv(
        "/Users/<USER>/Documents/Work/BlockScholes/Research_Vol_Dashboard/funding_rates.csv"
    )


def grab_historical_alt_perps(
    start_time: int, end_time: int, interval_time: str
) -> dict:
    return asyncio.run(
        fetch_all_symbols(
            symbols=BYBIT_PERP_INSTRUMENTS,
            start_time=start_time,
            end_time=end_time,
            limit=200,
            interval_time=interval_time,
            category="linear",
        )
    )


def process_query_results(
    query_results: Any, interval_time: str
) -> pd.DataFrame:
    dfs = []
    for symbol, data_points in query_results.items():
        logging.info(f"Data for symbol: {symbol}")

        symbol_df = pd.DataFrame(data_points).rename(
            columns={
                "openInterestTimestamp": "timestamp",
                "openInterest": "oi",
                "fundingRate": "rate",
                "fundingRateTimestamp": "timestamp",
            }
        )
        symbol_df["qualified_name"] = (
            f"bybit.perpetual.{symbol}.{interval_time}.funding.rate"
        )
        dfs.append(symbol_df)

    all_dfs = pd.concat(dfs)
    all_dfs["timestamp"] = (all_dfs["timestamp"].astype(int) * 1e6).astype(int)
    cwd = Path.cwd()
    filename = "Altcoin_funding_rates.csv"
    filepath = cwd / filename
    all_dfs.to_csv(filepath)
    return all_dfs


if __name__ == "__main__":
    main()
