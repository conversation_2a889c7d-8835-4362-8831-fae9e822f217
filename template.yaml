AWSTemplateFormatVersion: "2010-09-09"
Description: Research Vol Dashboard

Resources:
  researchVolDashboardLambdaFunctionRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: researchVolDashboardLambdaFunctionRole
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: "sts:AssumeRole"
      Policies:
        - PolicyName: ResearchVolDashboardLambdaPolicy
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              # Logs permissions specific to this Lambda function
              - Effect: Allow
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                Resource: 
                  - !Sub "arn:aws:logs:${AWS::Region}:${AWS::AccountId}:log-group:/aws/lambda/ResearchVolDashboard*"
      ManagedPolicyArns:
        - !Sub "arn:aws:iam::${AWS::AccountId}:policy/allServicesDynamoDBPolicy"
        - !Sub "arn:aws:iam::${AWS::AccountId}:policy/LambdaFunctionsS3Policy"
        - !Sub "arn:aws:iam::${AWS::AccountId}:policy/CalcSSMReadPolicy"

  researchVolDashboardFunction:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: ResearchVolDashboardLambdaFunction
      PackageType: Image
      Role: !GetAtt researchVolDashboardLambdaFunctionRole.Arn
      Timeout: 600
      MemorySize: 3538
      Architectures:
        - arm64
      Environment:
        Variables:
          LOG_LEVEL: INFO
      Code:
        ImageUri: !Sub "${AWS::AccountId}.dkr.ecr.${AWS::Region}.amazonaws.com/research-vol-dashboard-function:latest"

  ResearchVolDashboardLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: "/aws/lambda/ResearchVolDashboardLambdaFunction"
      RetentionInDays: 14

  researchVolDashboardErrorMetricFilter:
    Type: AWS::Logs::MetricFilter
    Properties:
      LogGroupName: !Sub "/aws/lambda/${researchVolDashboardFunction}"
      FilterName: "ResearchVolDashboard_Lambda_Errors"
      FilterPattern: "ERROR"
      MetricTransformations:
        - MetricValue: "1"
          MetricNamespace: "CalcLambdas"
          MetricName: "ResearchVolDashboard_Lambda_Errors"
          Unit: "Count"

Outputs:
  researchVolDashboardFunction:
    Description: "researchVolDashboard Lambda Function ARN"
    Value: !GetAtt researchVolDashboardFunction.Arn
