name: Lambda Function Deployment
on:
  workflow_call:
    inputs:
      repository:
        description: "Github repository to run on"
        required: true
        type: string
      ref:
        description: "Branch ref to check out"
        required: false
        type: string
        default: "refs/heads/main"
      actor:
        description: "User who triggered workflow"
        required: false
        type: string
      template:
        description: "Lambda function template YAML file to use"
        required: true
        type: string
      cpuArchitecture:
        description: "CPU architecture to deploy"
        required: false
        type: string
        default: X86_64
      environment:
        description: "Environment to deploy to"
        required: false
        type: string
        default: staging
      region:
        description: "AWS Region to deploy to"
        required: false
        type: string
        default: eu-west-2
      python_version:
        description: "Python version"
        required: false
        type: string
        default: 3.11
      docker_file:
        description: "Docker file path to use"
        required: false
        type: string
        default: api/Dockerfile
      ecr_repository:
        description: "ECR repository to push image to"
        required: true
        type: string
      stack_name:
        description: "CloudFormation stack name"
        required: true
        type: string
      template_file:
        description: "CloudFormation template file"
        required: true
        type: string
      lambda_function_name:
        description: "Lambda function name"
        required: true
        type: string

jobs:
  deploy-function:
    name: Deploy Function
    runs-on: ubuntu-latest
    if: contains(inputs.environment, 'staging') || (contains(inputs.ref, 'production') || contains(from<PERSON>son('["dfiltz", "Cody-G-G", "tonymaynard97"]'), inputs.actor))
    steps:
      - name: Get current date
        id: date
        run: echo "date=$(date +'%Y-%m-%d_%H.%M')" >> $GITHUB_OUTPUT

      - name: Set up build variables for ${{ inputs.cpuArchitecture }} architecture
        run: |
          if [ "${{ inputs.cpuArchitecture }}" = "X86_64" ]; then
            echo "CPU_ARCH=x86_64" >> $GITHUB_ENV
            echo "PYTHON_DOCKER_VERSION=${{ inputs.python_version }}" >> $GITHUB_ENV
            echo "PLATFORM=linux/amd64" >> $GITHUB_ENV
            echo "DOCKER_FROM=python:${{ inputs.python_version }}" >> $GITHUB_ENV
          elif [ "${{ inputs.cpuArchitecture }}" = "ARM64" ]; then
            echo "CPU_ARCH=arm64" >> $GITHUB_ENV
            echo "PYTHON_DOCKER_VERSION=${{ inputs.python_version }}-arm64" >> $GITHUB_ENV
            echo "PLATFORM=linux/arm64" >> $GITHUB_ENV
            echo "DOCKER_FROM=arm64v8\/python:${{ inputs.python_version }}" >> $GITHUB_ENV
          else
            echo "Incorrect CPU Architecture: ${{ inputs.cpuArchitecture }}"
            exit 1
          fi
      - name: Set up ${{ inputs.environment }} credentials
        run: |
          if [ "${{ inputs.environment }}" = "staging" ]; then
            echo "AWS_ACCESS_KEY=${{ secrets.AWS_ACCESS_KEY_ID_STAG }}" >> $GITHUB_ENV
            echo "AWS_SECRET=${{ secrets.AWS_SECRET_ACCESS_KEY_STAG }}" >> $GITHUB_ENV
          elif [ "${{ inputs.environment }}" = "prod" ]; then
            echo "AWS_ACCESS_KEY=${{ secrets.AWS_ACCESS_KEY_ID_PROD }}" >> $GITHUB_ENV
            echo "AWS_SECRET=${{ secrets.AWS_SECRET_ACCESS_KEY_PROD }}" >> $GITHUB_ENV
          else
            echo "Incorrect environment: ${{ inputs.environment }}"
            exit 1
          fi
      - name: Checkout
        uses: actions/checkout@v4
        with:
          repository: ${{ inputs.repository }}
          token: ${{ secrets.DEPLOY_CHAIN_TOKEN }}
          ref: ${{ inputs.ref }}
      - name: "Modify Lambda Definition YAML: ${{ inputs.template }}"
        uses: mikefarah/yq@master
        with:
          cmd: yq -i '.Resources.*Function.Properties.Architectures[0] = "${{ env.CPU_ARCH }}"' ${{ inputs.template }}
      - name: "Modify Dockerfile: ${{ inputs.docker_file }}"
        run: |
          sed -i "s/FROM .*python.*/FROM public.ecr.aws\/lambda\/python:${{ env.PYTHON_DOCKER_VERSION }}/" ${{ inputs.docker_file }}
          sed -i "s/python.* -m/python${{ inputs.python_version }} -m/" ${{ inputs.docker_file }}
      - name: Set up QEMU
        if: ${{ inputs.cpuArchitecture == 'ARM64' }}
        uses: docker/setup-qemu-action@v3
      - name: Set up Docker Buildx
        if: ${{ inputs.cpuArchitecture == 'ARM64' }}
        uses: docker/setup-buildx-action@v3
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ env.AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ env.AWS_SECRET }}
          aws-region: ${{ inputs.region }}
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          ACCESS_TOKEN: ${{ secrets.ACCESS_TOKEN }}
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: ${{ github.sha }}
          DATE: ${{ steps.date.outputs.date }}
          ECR_REPOSITORY: ${{ inputs.ecr_repository }}
        run: |
          # Build a docker container and
          # push it to ECR so that it can
          # be deployed to ECS.
          docker buildx build --platform=$PLATFORM  --provenance=false -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG -t $ECR_REGISTRY/$ECR_REPOSITORY:$DATE -t $ECR_REGISTRY/$ECR_REPOSITORY:latest -f ${{ inputs.docker_file }} --build-arg ACCESS_TOKEN=$ACCESS_TOKEN --push .
          echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT
      - name: Deploy to AWS CloudFormation
        uses: aws-actions/aws-cloudformation-github-deploy@v1
        with:
          name: ${{ inputs.stack_name }}
          template: ${{ inputs.template_file }}
          no-fail-on-empty-changeset: "1"
          capabilities: CAPABILITY_IAM, CAPABILITY_NAMED_IAM, CAPABILITY_AUTO_EXPAND
      - name: Update Lambda function to new image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: ${{ inputs.ecr_repository }}
        run: |
          aws lambda update-function-code \
            --function-name ${{ inputs.lambda_function_name }} \
            --image-uri $ECR_REGISTRY/$ECR_REPOSITORY:latest