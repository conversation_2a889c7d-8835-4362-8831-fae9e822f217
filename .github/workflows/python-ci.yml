name: Python CI

on:
  pull_request:

jobs:
  build:
    if: github.repository == 'blockscholes/researchVolDashboard'
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3
    - name: Install uv
      uses: astral-sh/setup-uv@v3
      with:
        version: "0.4.20"
    - name: "Set up Python"
      uses: actions/setup-python@v5
      with:
        python-version-file: ".python-version"
    - name: Setup netrc
      run: |
        echo -e "machine github.com\n  login ${{ secrets.ACCESS_TOKEN }}" > ~/.netrc
        chmod 0600 ~/.netrc

    - name: Install the project
      run: uv sync --all-extras --dev

    - name: Check code formatting with black
      run: uv run black --check .

    - name: Lint with ruff
      run: uv run ruff check .

    - name: Run tests
      run: uv run pytest