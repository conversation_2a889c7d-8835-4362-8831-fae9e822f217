{"version": "0.2.0", "configurations": [{"name": "Run and Debug App", "type": "debugpy", "request": "launch", "cwd": "${workspaceFolder}", "program": "${workspaceFolder}/app.py", "args": ["-event_type", "static", "-event_file", "falcon_trades.json", "-target_snapshot", "2025-06-23T16:00:00.000Z", "-listed_expiry", "25JUL25", "-lookback_days", "90"], "console": "integratedTerminal", "env": {"AWS_PROFILE": "prod"}}]}