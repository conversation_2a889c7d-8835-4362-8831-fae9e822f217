.grid-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 3 columns */
    grid-template-rows: repeat(5, 1fr); /* 5 rows */
    gap: 10px; /* space between grid items */
}

/*.grid-container {*/
/*    display: grid;*/
/*    grid-template-columns: auto auto;*/
/*    !* Two columns *!*/
/*    grid-gap: 20px;*/
/*    !* Spacing between grid items *!*/
/*    padding: 10px;*/
/*}*/

.grid-item {
    padding: 20px;
    text-align: center;
}

/* Optional: Styling for the toggle button */
#toggle-button {
    margin: 10px;
    padding: 5px 15px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.description {
    grid-column: 1 / span 3;  /* Span the description across all columns */
    text-align: center;      /* Center the text inside the description */
    /* Add other styling as needed */
}

body {
    background-color: #101A2E; /* Replace with your desired color */
}
/*!* Hover effect for the button *!*/
/*#toggle-button:hover {*/
/*    background-color: #0056b3;*/
/*}*/