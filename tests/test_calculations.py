import json
import logging
from datetime import UTC
from typing import Any

import numpy as np
import orjson
import pandas as pd
import pytest

from research_vol_dashboard.calculations import (
    CALCULATION_PIPELINES,
    _prepare_wing_spread_data,
    wing_spread_calc,
)
from research_vol_dashboard.typings import (
    FlexTimeseriesChart,
    FlexTimeSeriesTarget,
)
from tests.conftest import (
    convert_to_json_serializable,
    set_snapshot_path_to_parent,
)


@pytest.fixture
def snapshot(snapshot: Any) -> Any:
    return set_snapshot_path_to_parent(snapshot)


def test_prepare_wing_spread_data_with_real_data_snapshot(
    wing_spread_data_df: pd.DataFrame,
    chart_items: FlexTimeseriesChart,
    snapshot: Any,
) -> None:
    """Test _prepare_wing_spread_data with real data using pytest snapshots."""

    result = _prepare_wing_spread_data(wing_spread_data_df, chart_items)
    json_result = convert_to_json_serializable(result)
    json_string = json.dumps(json_result, indent=2, sort_keys=True)

    snapshot.assert_match(json_string, "prepare_wing_spread_data_result.json")


def test_full_wing_spread_pipeline_with_real_data_snapshot(
    wing_spread_data_df: pd.DataFrame,
    chart_items: FlexTimeseriesChart,
    snapshot: Any,
) -> None:
    """Test the full wing spread calculation pipeline with real data using pytest snapshots."""

    pipeline = CALCULATION_PIPELINES["WING_SPREAD_CALC"]

    prepared_data = pipeline.preparator(wing_spread_data_df, chart_items)
    result_df, plot_targets = pipeline.calculator(prepared_data)

    json_result = {
        "result_df": convert_to_json_serializable(result_df),
        "plot_targets": convert_to_json_serializable(plot_targets),
    }

    json_string = orjson.dumps(json_result, option=orjson.OPT_SORT_KEYS).decode(
        "utf-8"
    )

    snapshot.assert_match(json_string, "full_wing_spread_pipeline_result.json")


def test_prepare_wing_spread_data_success(
    mock_wing_spread_data_df: pd.DataFrame, chart_items: FlexTimeseriesChart
) -> None:
    """Test successful preparation of wing spread data."""
    result = _prepare_wing_spread_data(mock_wing_spread_data_df, chart_items)

    # Should have 5 qualified names
    assert len(result) == 5

    # Check that each qualified name has the expected structure
    expected_qns = [
        "v2composite.option.BTC.SVI.7d.1h.smile",
        "v2composite.option.BTC.SVI.30d.1h.smile",
        "v2composite.option.BTC.SVI.60d.1h.smile",
        "v2composite.option.BTC.SVI.90d.1h.smile",
        "v2composite.option.BTC.SVI.180d.1h.smile",
    ]

    for qn in expected_qns:
        assert qn in result
        assert (
            "10delta" in result[qn]
        )  # Changed from "wing_vol" to actual wing target
        assert "atm_vol" in result[qn]
        assert isinstance(result[qn]["10delta"], pd.Series)
        assert isinstance(result[qn]["atm_vol"], pd.Series)
        assert not result[qn]["10delta"].empty
        assert not result[qn]["atm_vol"].empty


def test_prepare_wing_spread_data_missing_target() -> None:
    """Test error when a qualified name has no wing targets."""
    data_df = pd.DataFrame(
        {
            "qualified_name": ["v-00004.v2composite.option.BTC.SVI.7d.1h.smile"]
            * 10,
            "atm": [0.7] * 10,
            "10delta": [0.75] * 10,
        }
    )
    data_df.index = pd.date_range("2024-01-01", periods=10, freq="h")

    # Chart with only ATM target (no wing targets)
    chart_items: FlexTimeseriesChart = {
        "yaxis_title": "Test",
        "chart_title": "Test Chart",
        "targets": [
            {
                "qualified_name": "v-00004.v2composite.option.BTC.SVI.7d.1h.smile",
                "target": "atm",
            }
        ],
    }

    with pytest.raises(
        ValueError,
        match="Wing spread calculation requires at least 1 wing target",
    ):
        _prepare_wing_spread_data(data_df, chart_items)


def test_prepare_wing_spread_data_double_atm_target(
    caplog: pytest.LogCaptureFixture,
) -> None:
    """Test error when a qualified name has multiple ATM targets."""
    data_df = pd.DataFrame(
        {
            "qualified_name": ["v-00004.v2composite.option.BTC.SVI.7d.1h.smile"]
            * 10,
            "atm": [0.7] * 10,
            "10delta": [0.75] * 10,
        }
    )
    data_df.index = pd.date_range("2024-01-01", periods=10, freq="h")

    # Chart with duplicate ATM targets
    chart_items: FlexTimeseriesChart = {
        "yaxis_title": "Test",
        "chart_title": "Test Chart",
        "targets": [
            {
                "qualified_name": "v2composite.option.BTC.SVI.7d.1h.smile",
                "target": "atm",
            },
            {
                "qualified_name": "v2composite.option.BTC.SVI.7d.1h.smile",
                "target": "atm",
            },
            {
                "qualified_name": "v2composite.option.BTC.SVI.7d.1h.smile",
                "target": "10delta",
            },
        ],
    }

    _prepare_wing_spread_data(data_df, chart_items)

    warning_records = [
        record.message
        for record in caplog.records
        if record.levelno == logging.WARNING
    ]

    # Verify that the duplicate warning was logged
    assert (
        "Found duplicate targets for qualified name v2composite.option.BTC.SVI.7d.1h.smile:"
        " ['atm']. Chart title: Test Chart. Duplicates will be ignored."
        in warning_records
    )


def test_prepare_wing_spread_data_no_atm_target() -> None:
    """Test error when no ATM target is found."""
    data_df = pd.DataFrame(
        {
            "qualified_name": ["v-00004.v2composite.option.BTC.SVI.7d.1h.smile"]
            * 10,
            "10delta": [0.75] * 10,
            "25delta": [0.72] * 10,
        }
    )
    data_df.index = pd.date_range("2024-01-01", periods=10, freq="h")

    chart_items: FlexTimeseriesChart = {
        "yaxis_title": "Test",
        "chart_title": "Test Chart",
        "targets": [
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.BTC.SVI.7d.1h.smile",
                target="10delta",
            ),
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.BTC.SVI.7d.1h.smile",
                target="25delta",
            ),
        ],
    }

    with pytest.raises(
        ValueError,
        match="Wing spread calculation requires exactly 1 ATM target",
    ):
        _prepare_wing_spread_data(data_df, chart_items)


def test_prepare_wing_spread_data_multiple_wing_targets() -> None:
    """Test successful preparation with multiple wing targets per qualified name."""
    data_df = pd.DataFrame(
        {
            "qualified_name": ["v-00004.v2composite.option.BTC.SVI.7d.1h.smile"]
            * 10,
            "atm": [0.7] * 10,
            "10delta": [0.75] * 10,
            "25delta": [0.72] * 10,
        }
    )
    data_df.index = pd.date_range("2024-01-01", periods=10, freq="h")

    chart_items: FlexTimeseriesChart = {
        "yaxis_title": "Test",
        "chart_title": "Test Chart",
        "targets": [
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.BTC.SVI.7d.1h.smile",
                target="atm",
            ),
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.BTC.SVI.7d.1h.smile",
                target="10delta",
            ),
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.BTC.SVI.7d.1h.smile",
                target="25delta",
            ),
        ],
    }

    result = _prepare_wing_spread_data(data_df, chart_items)

    # Should have 1 qualified name
    assert len(result) == 1
    qn = "v2composite.option.BTC.SVI.7d.1h.smile"

    # Check structure includes ATM and all wing targets
    assert qn in result
    assert "atm_vol" in result[qn]
    assert "10delta" in result[qn]
    assert "25delta" in result[qn]

    # Verify all are pandas Series and not empty
    for key in ["atm_vol", "10delta", "25delta"]:
        assert isinstance(result[qn][key], pd.Series)
        assert not result[qn][key].empty


def test_wing_spread_calc(
    mock_wing_spread_data_df: pd.DataFrame, chart_items: FlexTimeseriesChart
) -> None:
    """Test the wing spread calculation function."""
    # First prepare the data
    prepared_data = _prepare_wing_spread_data(
        mock_wing_spread_data_df, chart_items
    )

    # Run the calculation
    result_df, plot_targets = wing_spread_calc(prepared_data)

    # Check the result DataFrame
    assert isinstance(result_df, pd.DataFrame)
    assert result_df.index.name == "timestamp"
    assert "qualified_name" in result_df.columns

    expected_targets = [
        "BTC_10delta_7d",
        "BTC_10delta_30d",
        "BTC_10delta_60d",
        "BTC_10delta_90d",
        "BTC_10delta_180d",
    ]
    for target in expected_targets:
        assert target in result_df.columns

    # Check plot targets
    assert isinstance(plot_targets, list)
    assert len(plot_targets) == 5  # One for each tenor

    for _target in plot_targets:
        assert "qualified_name" in _target
        assert "target" in _target
        assert "trace_title" in _target
        assert _target["target"] in expected_targets
        assert _target["trace_title"] == _target["target"]
        assert isinstance(result_df.index, pd.DatetimeIndex)


def test_calculation_pipeline_integration(
    mock_wing_spread_data_df: pd.DataFrame, chart_items: FlexTimeseriesChart
) -> None:
    """Test the full calculation pipeline."""
    pipeline = CALCULATION_PIPELINES["WING_SPREAD_CALC"]

    # Run the preparator
    prepared_data = pipeline.preparator(mock_wing_spread_data_df, chart_items)

    # Run the calculator
    result_df, plot_targets = pipeline.calculator(prepared_data)

    # Verify the results
    assert isinstance(result_df, pd.DataFrame)
    assert isinstance(plot_targets, list)
    assert not result_df.empty
    assert len(plot_targets) > 0


def test_prepare_wing_spread_data_missing_columns() -> None:
    """Test error when required columns are missing from data."""
    data_df = pd.DataFrame(
        {
            "qualified_name": ["v-00004.v2composite.option.BTC.SVI.7d.1h.smile"]
            * 10,
            "atm": [0.7] * 10,
            # Missing "10delta" column
        }
    )
    data_df.index = pd.date_range("2024-01-01", periods=10, freq="h")

    chart_items: FlexTimeseriesChart = {
        "yaxis_title": "Test",
        "chart_title": "Test Chart",
        "targets": [
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.BTC.SVI.7d.1h.smile",
                target="atm",
            ),
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.BTC.SVI.7d.1h.smile",
                target="25delta",
            ),
        ],
    }

    with pytest.raises(ValueError, match="Missing columns"):
        _prepare_wing_spread_data(data_df, chart_items)


def test_prepare_wing_spread_data_empty_data() -> None:
    """Test handling of empty data for a qualified name."""
    # Create data that won't match any qualified names
    data_df = pd.DataFrame(
        {
            "qualified_name": ["different.qualified.name"] * 10,
            "atm": [0.7] * 10,
            "10delta": [0.75] * 10,
        }
    )
    data_df.index = pd.date_range("2024-01-01", periods=10, freq="h")

    chart_items: FlexTimeseriesChart = {
        "yaxis_title": "Test",
        "chart_title": "Test Chart",
        "targets": [
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.BTC.SVI.7d.1h.smile",
                target="atm",
            ),
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.BTC.SVI.7d.1h.smile",
                target="25delta",
            ),
        ],
    }

    result = _prepare_wing_spread_data(data_df, chart_items)

    # Should return empty dict since no matching data found
    assert len(result) == 0


def test_prepare_wing_spread_data_multiple_qns_multiple_targets() -> None:
    """Test successful preparation with multiple qualified names, each having multiple wing targets."""
    data_df = pd.DataFrame(
        {
            "qualified_name": (
                ["v-00004.v2composite.option.BTC.SVI.7d.1h.smile"] * 10
                + ["v-00004.v2composite.option.BTC.SVI.30d.1h.smile"] * 10
                + ["v-00004.v2composite.option.ETH.SVI.7d.1h.smile"] * 10
            ),
            "atm": [0.7] * 30,
            "10delta": [0.75] * 30,
            "25delta": [0.72] * 30,
        }
    )
    data_df.index = pd.date_range("2024-01-01", periods=30, freq="h")

    chart_items: FlexTimeseriesChart = {
        "yaxis_title": "Test",
        "chart_title": "Multi QN Multi Target Test",
        "targets": [
            # BTC 7d targets
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.BTC.SVI.7d.1h.smile",
                target="atm",
            ),
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.BTC.SVI.7d.1h.smile",
                target="10delta",
            ),
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.BTC.SVI.7d.1h.smile",
                target="25delta",
            ),
            # BTC 30d targets
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.BTC.SVI.30d.1h.smile",
                target="atm",
            ),
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.BTC.SVI.30d.1h.smile",
                target="10delta",
            ),
            # ETH 7d targets
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.ETH.SVI.7d.1h.smile",
                target="atm",
            ),
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.ETH.SVI.7d.1h.smile",
                target="25delta",
            ),
        ],
    }

    result = _prepare_wing_spread_data(data_df, chart_items)

    # Should have 3 qualified names
    assert len(result) == 3

    expected_qns = [
        "v2composite.option.BTC.SVI.7d.1h.smile",
        "v2composite.option.BTC.SVI.30d.1h.smile",
        "v2composite.option.ETH.SVI.7d.1h.smile",
    ]

    for qn in expected_qns:
        assert qn in result
        assert "atm_vol" in result[qn]
        assert isinstance(result[qn]["atm_vol"], pd.Series)
        assert not result[qn]["atm_vol"].empty

    # Check BTC 7d has 2 wing targets
    btc_7d = result["v2composite.option.BTC.SVI.7d.1h.smile"]
    assert "10delta" in btc_7d
    assert "25delta" in btc_7d
    assert len([k for k in btc_7d.keys() if k != "atm_vol"]) == 2

    # Check BTC 30d has 1 wing targets
    btc_30d = result["v2composite.option.BTC.SVI.30d.1h.smile"]
    assert "10delta" in btc_30d
    assert len([k for k in btc_30d.keys() if k != "atm_vol"]) == 1

    # Check ETH 7d has 1 wing targets
    eth_7d = result["v2composite.option.ETH.SVI.7d.1h.smile"]
    assert "25delta" in eth_7d
    assert len([k for k in eth_7d.keys() if k != "atm_vol"]) == 1

    # Verify all wing targets have valid data
    for qn_data in result.values():
        for _key, series in qn_data.items():
            assert isinstance(series, pd.Series)
            assert not series.empty


def test_wing_spread_calc_multiple_qns_multiple_targets() -> None:
    """Test wing spread calculation with multiple qualified names, each having multiple wing targets."""

    periods = 1000
    timestamps = pd.date_range(
        start="2024-01-01 00:00:00",
        periods=periods,
        freq="h",
        tz=UTC,
    )

    data = []

    # Set random seed for reproducible results in tests
    rng = np.random.default_rng(42)

    # BTC 7d: has atm, 10delta, 25delta
    for ts in timestamps:
        data.append(
            {
                "timestamp": ts,
                "qualified_name": "v-00004.v2composite.option.BTC.SVI.7d.1h.smile",
                "atm": 0.7 + rng.normal(0, 0.05),
                "10delta": 0.75 + rng.normal(0, 0.05),
                "25delta": 0.72 + rng.normal(0, 0.05),
            }
        )

        # BTC 30d: has atm, 10delta (no 25delta)
        data.append(
            {
                "timestamp": ts,
                "qualified_name": "v-00004.v2composite.option.BTC.SVI.30d.1h.smile",
                "atm": 0.7 + rng.normal(0, 0.05),
                "10delta": 0.75 + rng.normal(0, 0.05),
            }
        )

        # ETH 7d: has atm, 25delta (no 10delta)
        data.append(
            {
                "timestamp": ts,
                "qualified_name": "v-00004.v2composite.option.ETH.SVI.7d.1h.smile",
                "atm": 0.7 + rng.normal(0, 0.05),
                "25delta": 0.72 + rng.normal(0, 0.05),
            }
        )

    data_df = pd.DataFrame(data)
    data_df.set_index("timestamp", inplace=True)

    chart_items: FlexTimeseriesChart = {
        "yaxis_title": "Test",
        "chart_title": "Multi QN Multi Target Wing Spread Test",
        "targets": [
            # BTC 7d targets
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.BTC.SVI.7d.1h.smile",
                target="atm",
            ),
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.BTC.SVI.7d.1h.smile",
                target="10delta",
            ),
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.BTC.SVI.7d.1h.smile",
                target="25delta",
            ),
            # BTC 30d targets
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.BTC.SVI.30d.1h.smile",
                target="atm",
            ),
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.BTC.SVI.30d.1h.smile",
                target="10delta",
            ),
            # ETH 7d targets
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.ETH.SVI.7d.1h.smile",
                target="atm",
            ),
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.ETH.SVI.7d.1h.smile",
                target="25delta",
            ),
        ],
    }

    # First prepare the data
    prepared_data = _prepare_wing_spread_data(data_df, chart_items)

    # Run the wing spread calculation
    result_df, plot_targets = wing_spread_calc(prepared_data)

    # Verify result DataFrame structure
    assert isinstance(result_df, pd.DataFrame)
    assert result_df.index.name == "timestamp"
    assert "qualified_name" in result_df.columns
    assert not result_df.empty

    expected_tenors = [
        "BTC_10delta_7d",
        "BTC_25delta_7d",
        "BTC_10delta_30d",
        "ETH_25delta_7d",
    ]
    for tenor in expected_tenors:
        assert tenor in result_df.columns

    # Verify we have the correct qualified names in the data
    unique_qns = result_df["qualified_name"].unique()
    expected_wing_spread_qns = [
        "v2composite.option.BTC.SVI.7d.10delta.1h.wing_spread",
        "v2composite.option.BTC.SVI.7d.25delta.1h.wing_spread",
        "v2composite.option.BTC.SVI.30d.10delta.1h.wing_spread",
        "v2composite.option.ETH.SVI.7d.25delta.1h.wing_spread",
    ]

    # Each QN should appear in results
    for expected_qn in expected_wing_spread_qns:
        assert expected_qn in unique_qns

    # Verify plot targets
    assert isinstance(plot_targets, list)
    assert len(plot_targets) >= 2  # At least one for each tenor

    # Check plot target structure
    for target in plot_targets:
        assert "qualified_name" in target
        assert "target" in target
        assert "trace_title" in target
        assert target["target"] in expected_tenors
        assert target["qualified_name"].endswith(".wing_spread")

    # Verify we have proper datetime index
    assert isinstance(result_df.index, pd.DatetimeIndex)

    # Verify data contains numeric values (wing spread calculations)
    for tenor in expected_tenors:
        tenor_data = result_df[tenor].dropna()
        if not tenor_data.empty:
            assert tenor_data.dtype in ["float64", "float32"]

    # Verify we have data for each qualified name and tenor combination
    # BTC 7d should have data (has both 10delta and 25delta wing targets)
    btc_7d_10delta_data = result_df[
        result_df["qualified_name"]
        == "v2composite.option.BTC.SVI.7d.10delta.1h.wing_spread"
    ]["BTC_10delta_7d"]
    assert not btc_7d_10delta_data.dropna().empty
    btc_7d_25_delta_data = result_df[
        result_df["qualified_name"]
        == "v2composite.option.BTC.SVI.7d.25delta.1h.wing_spread"
    ]["BTC_25delta_7d"]
    assert not btc_7d_25_delta_data.dropna().empty

    # BTC 30d should have data (has 10delta wing target)
    btc_30d_10delta_data = result_df[
        result_df["qualified_name"]
        == "v2composite.option.BTC.SVI.30d.10delta.1h.wing_spread"
    ]["BTC_10delta_30d"]
    assert not btc_30d_10delta_data.dropna().empty

    # ETH 7d should have data (has 25delta wing target)
    eth_7d_25_delta_data = result_df[
        result_df["qualified_name"]
        == "v2composite.option.ETH.SVI.7d.25delta.1h.wing_spread"
    ]["ETH_25delta_7d"]
    assert not eth_7d_25_delta_data.dropna().empty
