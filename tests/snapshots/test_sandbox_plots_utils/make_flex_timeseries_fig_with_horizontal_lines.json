{"data": [{"line": {"color": "#247CFF"}, "mode": "lines", "name": "ATM Vol", "showlegend": true, "type": "scatter", "x": [1704067200000000000, 1704070800000000000, 1704074400000000000, 1704078000000000000, 1704081600000000000, 1704085200000000000, 1704088800000000000, 1704092400000000000, 1704096000000000000, 1704099600000000000], "y": {"bdata": "AAAAAAAASUAAAAAAAIBJQAAAAAAAAEpAAAAAAACASkAAAAAAAABLQAEAAAAAgEtAAQAAAAAATEABAAAAAIBMQP///////0xAAAAAAACATUA=", "dtype": "f8"}}, {"line": {"color": "#FFCD00"}, "mode": "lines", "name": "10 Delta Vol", "showlegend": true, "type": "scatter", "x": [1704067200000000000, 1704070800000000000, 1704074400000000000, 1704078000000000000, 1704081600000000000, 1704085200000000000, 1704088800000000000, 1704092400000000000, 1704096000000000000, 1704099600000000000], "y": {"bdata": "AAAAAAAATkAAAAAAAIBOQAAAAAAAAE9AAAAAAACAT0AAAAAAAABQQAAAAAAAQFBA//////9/UEAAAAAAAMBQQAAAAAAAAFFAAAAAAABAUUA=", "dtype": "f8"}}], "layout": {"annotations": [{"showarrow": false, "text": "Target Level", "x": 1, "xanchor": "left", "xref": "x domain", "y": 0.55, "yanchor": "middle", "yref": "y"}, {"showarrow": false, "text": "Upper Bound", "x": 1, "xanchor": "left", "xref": "x domain", "y": 0.65, "yanchor": "middle", "yref": "y"}], "font": {"color": "white", "size": 20}, "height": 650, "images": [{"opacity": 0.5, "sizex": 0.4, "sizey": 0.4, "source": "data:image/png;base64,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", "x": 0.5, "xanchor": "center", "xref": "paper", "y": 0.5, "yanchor": "middle", "yref": "paper"}], "legend": {"orientation": "h", "x": 0.5, "xanchor": "center", "y": 1.1, "yanchor": "top"}, "margin": {"b": 0, "l": 150, "pad": 4, "r": 30, "t": 0}, "paper_bgcolor": "#101A2E", "plot_bgcolor": "#101A2E", "shapes": [{"line": {"color": "rgba(255, 0, 0, 0.7)", "dash": "dash", "width": 2}, "type": "line", "x0": 0, "x1": 1, "xref": "x domain", "y0": 0.55, "y1": 0.55, "yref": "y"}, {"line": {"color": "rgba(0, 255, 0, 0.7)", "dash": "solid", "width": 1}, "type": "line", "x0": 0, "x1": 1, "xref": "x domain", "y0": 0.65, "y1": 0.65, "yref": "y"}], "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "heatmap"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermap": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermap"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "sequentialminus": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"font": {"color": "white", "size": 30}, "x": 0.082, "xanchor": "center", "y": 0.97, "yanchor": "top"}, "width": 1450, "xaxis": {"gridcolor": "#293e68", "gridwidth": 1, "showgrid": true, "zeroline": false}, "yaxis": {"gridcolor": "#293e68", "gridwidth": 1, "range": [-0.01, 70.7], "showgrid": true, "tickmode": "array", "ticktext": ["0.00%", "7.78%", "15.56%", "23.33%", "31.11%", "38.89%", "46.67%", "54.44%", "62.22%", "70.00%"], "tickvals": [0.0, 7.777777777777778, 15.555555555555555, 23.333333333333332, 31.11111111111111, 38.888888888888886, 46.666666666666664, 54.44444444444444, 62.22222222222222, 70.0], "title": {"text": "Volatility"}, "zeroline": false}}}