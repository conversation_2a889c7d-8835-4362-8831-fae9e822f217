import base64
import json
import pathlib
from datetime import UTC, datetime
from io import Bytes<PERSON>
from typing import Any
from unittest.mock import patch

import pandas as pd
import pytest
from PIL import Image, UnidentifiedImageError

from app import lambda_handler
from research_vol_dashboard.typings import GrabbedR<PERSON>ult
from research_vol_dashboard.utils import is_lambda_context

from .conftest import load_fixture


def test_lambda_handler_with_mocked_process_event(
    mock_event: dict[Any, Any],
    mock_context: dict[Any, Any],
    mock_retrieve_data_timeseries: dict[str, GrabbedResult],
) -> None:
    with patch("app.retrieve_data") as mock_retrieve_data:
        mock_retrieve_data.return_value = mock_retrieve_data_timeseries
        response = lambda_handler(mock_event, mock_context)  # type: ignore

        assert is_lambda_context()
        assert response["statusCode"] == 200

        body = json.loads(response["body"])

        assert body["output"]["status"] == 0
        assert body["output"]["msg"] == "Success"

        charts_dict = body["output"]["results"]

        assert isinstance(charts_dict, dict)
        assert charts_dict

        for _chart_type, charts in charts_dict.items():
            assert isinstance(charts, dict)
            for chart_name, payload in charts.items():
                encoded_image = payload["image"]
                # Decode the base64 string
                image_bytes = base64.b64decode(encoded_image)

                # Check that the image bytes start with the PNG signature
                assert image_bytes.startswith(
                    b"\x89PNG\r\n\x1a\n"
                ), f"The image {chart_name} does not start with a valid PNG signature."

                # Try to open the image using PIL to ensure it's a valid PNG
                try:
                    image = Image.open(BytesIO(image_bytes))
                    assert (
                        image.format == "PNG"
                    ), f"The image {chart_name} is not recognized as a PNG format."
                except UnidentifiedImageError:
                    pytest.fail(
                        f"The image {chart_name} could not be identified as a valid image."
                    )


def test_lambda_handler_with_include_latest_datapoints(
    mock_context: dict[Any, Any],
    mock_wing_spread_event: dict[Any, Any],
) -> None:

    # Load the snapshot data to get expected values
    snapshot_path = (
        pathlib.Path(__file__).parent
        / "snapshots"
        / "test_calculations"
        / "full_wing_spread_pipeline_result.json"
    )
    with open(snapshot_path) as f:
        snapshot_data = json.load(f)

    target_column = "BTC_10delta_7d"

    # Extract the result_df from snapshot and find max timestamp efficiently
    result_df_data = snapshot_data["result_df"]

    # Convert to pandas Series and find max index
    index_series = pd.Series(result_df_data["index"])
    expected_max_timestamp = int(
        datetime.fromisoformat(index_series.max())
        .replace(tzinfo=UTC)
        .timestamp()
        * 1e9
    )
    max_idx = index_series.idxmax()
    target_column_idx = result_df_data["columns"].index(target_column)
    expected_max_result = result_df_data["data"][max_idx][target_column_idx]

    with patch("app.retrieve_data") as mock_retrieve_data:
        mock_retrieve_data.return_value = {
            "v2timeseries": GrabbedResult(
                data=load_fixture("timeseries/wing_spread_data.json"),
                instruments=[],
            )
        }

        response = lambda_handler(mock_wing_spread_event, mock_context)  # type: ignore

        assert is_lambda_context()
        assert response["statusCode"] == 200

        body = json.loads(response["body"])

        assert body["output"]["status"] == 0
        assert body["output"]["msg"] == "Success"

        charts_dict = body["output"]["results"]

        assert isinstance(charts_dict, dict)
        assert charts_dict

        for _chart_type, charts in charts_dict.items():
            assert isinstance(charts, dict)
            for chart_name, payload in charts.items():
                encoded_image = payload["image"]

                # Decode the base64 string
                image_bytes = base64.b64decode(encoded_image)

                # Check that the image bytes start with the PNG signature
                assert image_bytes.startswith(
                    b"\x89PNG\r\n\x1a\n"
                ), f"The image {chart_name} does ncot start with a valid PNG signature."

                # Try to open the image using PIL to ensure it's a valid PNG
                try:
                    image = Image.open(BytesIO(image_bytes))
                    assert (
                        image.format == "PNG"
                    ), f"The image {chart_name} is not recognized as a PNG format."
                except UnidentifiedImageError:
                    pytest.fail(
                        f"The image {chart_name} could not be identified as a valid image."
                    )

                assert payload["data"] == {
                    "BTC_10delta_7d": [
                        {
                            "timestamp": expected_max_timestamp,
                            "value": expected_max_result,
                        }
                    ]
                }
