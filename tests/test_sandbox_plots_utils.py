import json
from typing import Any

import pandas as pd
import pytest

from research_vol_dashboard.sandbox_plots_utils import make_flex_timeseries_fig
from research_vol_dashboard.typings import FlexTimeSeriesTarget
from tests.conftest import (
    convert_to_json_serializable,
    set_snapshot_path_to_parent,
)


@pytest.fixture
def snapshot(snapshot: Any) -> Any:
    return set_snapshot_path_to_parent(snapshot)


@pytest.fixture
def sample_timeseries_data() -> pd.DataFrame:
    """Create sample timeseries data for testing."""
    timestamps = pd.date_range("2024-01-01", periods=10, freq="h")
    data = []

    for i, ts in enumerate(timestamps):
        data.append(
            {
                "timestamp": ts,
                "qualified_name": "test.option.BTC.SVI.7d.1h.smile",
                "atm": 0.5 + i * 0.01,  # Values from 0.5 to 0.59
                "10delta": 0.6 + i * 0.01,  # Values from 0.6 to 0.69
            }
        )

    df = pd.DataFrame(data)
    df.set_index("timestamp", inplace=True)
    return df


@pytest.fixture
def sample_targets() -> list[FlexTimeSeriesTarget]:
    """Create sample targets for testing."""
    return [
        {
            "qualified_name": "test.option.BTC.SVI.7d.1h.smile",
            "target": "atm",
            "trace_title": "ATM Vol",
            "color": "#247CFF",
        },
        {
            "qualified_name": "test.option.BTC.SVI.7d.1h.smile",
            "target": "10delta",
            "trace_title": "10 Delta Vol",
            "color": "#FFCD00",
        },
    ]


def test_make_flex_timeseries_fig_without_horizontal_lines(
    sample_timeseries_data: pd.DataFrame,
    sample_targets: list[FlexTimeSeriesTarget],
) -> None:
    """Test make_flex_timeseries_fig without horizontal lines (baseline functionality)."""
    fig = make_flex_timeseries_fig(
        data_df=sample_timeseries_data,
        target_to_plot=sample_targets,
        chart_name="test_chart",
        yaxis_title="Volatility",
        chart_type="timeseries",
        chart_title="Test Chart",
        tickprefix=None,
        ticksuffix="%",
        horizontal_lines=None,
    )

    # Basic assertions
    assert fig is not None
    assert len(fig.data) == 2  # Two traces for the two targets
    assert fig.data[0].name == "ATM Vol"
    assert fig.data[1].name == "10 Delta Vol"


def test_make_flex_timeseries_fig_with_horizontal_lines(
    sample_timeseries_data: pd.DataFrame,
    sample_targets: list[FlexTimeSeriesTarget],
) -> None:
    """Test make_flex_timeseries_fig with horizontal lines."""
    horizontal_lines = [
        {
            "y": 0.55,
            "color": "red",
            "line_width": 2,
            "line_dash": "dash",
            "label": "Target Level",
        },
        {
            "y": 0.65,
            "color": "green",
            "line_width": 1,
            "label": "Upper Bound",
        },
    ]

    fig = make_flex_timeseries_fig(
        data_df=sample_timeseries_data,
        target_to_plot=sample_targets,
        chart_name="test_chart_with_hlines",
        yaxis_title="Volatility",
        chart_type="timeseries",
        chart_title="Test Chart with Horizontal Lines",
        tickprefix=None,
        ticksuffix="%",
        horizontal_lines=horizontal_lines,
    )

    # Basic assertions
    assert fig is not None
    assert len(fig.data) == 2  # Two traces for the two targets

    # Check that horizontal lines were added (they appear as shapes in the layout)
    assert hasattr(fig.layout, "shapes")
    # Plotly's add_hline creates shapes, so we should have 2 shapes for our 2 horizontal lines
    assert len(fig.layout.shapes) == 2


def test_make_flex_timeseries_fig_horizontal_lines_affect_y_range(
    sample_timeseries_data: pd.DataFrame,
    sample_targets: list[FlexTimeSeriesTarget],
) -> None:
    """Test that horizontal lines affect the y-axis range calculation."""
    # Add horizontal lines that extend beyond the data range
    horizontal_lines = [
        {"y": 0.3},  # Below data range (data starts at 0.5)
        {"y": 0.8},  # Above data range (data ends at 0.69)
    ]

    fig = make_flex_timeseries_fig(
        data_df=sample_timeseries_data,
        target_to_plot=sample_targets,
        chart_name="test_chart_extended_range",
        yaxis_title="Volatility",
        chart_type="timeseries",
        chart_title="Test Chart Extended Range",
        tickprefix=None,
        ticksuffix="%",
        horizontal_lines=horizontal_lines,
    )

    # The y-axis range should be expanded to include the horizontal lines
    y_range = fig.layout.yaxis.range
    assert y_range is not None
    assert y_range[0] <= 0.3  # Should include the lower horizontal line
    assert y_range[1] >= 0.8  # Should include the upper horizontal line


def test_make_flex_timeseries_fig_horizontal_lines_snapshot(
    sample_timeseries_data: pd.DataFrame,
    sample_targets: list[FlexTimeSeriesTarget],
    snapshot: Any,
) -> None:
    """Test make_flex_timeseries_fig with horizontal lines using pytest snapshots."""
    horizontal_lines = [
        {
            "y": 0.55,
            "color": "rgba(255, 0, 0, 0.7)",
            "line_width": 2,
            "line_dash": "dash",
            "label": "Target Level",
        },
        {
            "y": 0.65,
            "color": "rgba(0, 255, 0, 0.7)",
            "line_width": 1,
            "label": "Upper Bound",
        },
    ]

    fig = make_flex_timeseries_fig(
        data_df=sample_timeseries_data,
        target_to_plot=sample_targets,
        chart_name="test_chart_snapshot",
        yaxis_title="Volatility",
        chart_type="timeseries",
        chart_title="Test Chart Snapshot",
        tickprefix=None,
        ticksuffix="%",
        horizontal_lines=horizontal_lines,
    )

    # Convert figure to JSON-serializable format for snapshot testing
    fig_dict = fig.to_dict()
    json_result = convert_to_json_serializable(fig_dict)
    json_string = json.dumps(json_result, indent=2, sort_keys=True)

    snapshot.assert_match(
        json_string, "make_flex_timeseries_fig_with_horizontal_lines.json"
    )
