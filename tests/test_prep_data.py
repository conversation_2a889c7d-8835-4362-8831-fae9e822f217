import pandas as pd

from research_vol_dashboard.prep_data import _prep_timeseries_data
from research_vol_dashboard.typings import FlexTimeseriesChart


def test_prep_timeseries_data_mixed_before_and_after_start() -> None:
    """Test _prep_timeseries_data when data exists both before and after the start time."""
    timeseries_df = pd.DataFrame(
        {
            "timestamp": [
                1645530465000000000,  # 2022-02-22 11:47:45 (before start)
                1645530540000000000,  # 2022-02-22 11:49:00 (after start)
                1645530600000000000,  # 2022-02-22 11:50:00 (after start)
            ],
            "qualified_name": [
                "exchange.symbol.BTC_USD.params",
                "exchange.symbol.BTC_USD.params",
                "exchange.symbol.BTC_USD.params",
            ],
            "px": [102, 200, 300],
        }
    )

    start = "2022-02-22T11:48:00Z"
    end = "2022-02-22T11:51:00Z"

    charts: dict[str, FlexTimeseriesChart] = {
        "chart1": {
            "yaxis_title": "Price",
            "chart_title": "Test Chart",
            "targets": [
                {
                    "qualified_name": "exchange.symbol.BTC_USD.params",
                    "target": "px",
                    "trace_title": "BTC Price",
                    "resample_config": {
                        "interval": "minute",
                        "periods": 1,
                    },
                }
            ],
        }
    }

    # Expected: value 102 should be forward-filled to start time
    # The function should mutate the qualified_name with a suffix
    expected_qn = "exchange.symbol.BTC_USD.params.1_minute"

    # fmt: off
    # Expected DataFrame combining resampled and original data
    expected_df = pd.DataFrame(
        [
            # Resampled data (with DatetimeIndex)
            {"px": 102,"qualified_name": "exchange.symbol.BTC_USD.params.1_minute","timestamp": 1645530480000000000}, # "2022-02-22T11:48:00"
            {"px": 200,"qualified_name": "exchange.symbol.BTC_USD.params.1_minute","timestamp": 1645530540000000000}, # "2022-02-22T11:49:00"
            {"px": 300,"qualified_name": "exchange.symbol.BTC_USD.params.1_minute","timestamp": 1645530600000000000}, # "2022-02-22T11:50:00"
            {"px": 300,"qualified_name": "exchange.symbol.BTC_USD.params.1_minute","timestamp": 1645530660000000000}, # "2022-02-22T11:51:00"
            # Original data (with numeric index)
            {"px": 102,"qualified_name": "exchange.symbol.BTC_USD.params","timestamp": 1645530465000000000},
            {"px": 200,"qualified_name": "exchange.symbol.BTC_USD.params","timestamp": 1645530540000000000},
            {"px": 300,"qualified_name": "exchange.symbol.BTC_USD.params","timestamp": 1645530600000000000},
        ]
    )

    # Set the expected index structure
    # First 4 rows (resampled data) should have DatetimeIndex
    resampled_index = pd.DatetimeIndex(
        [
            pd.Timestamp("2022-02-22T11:48:00"),
            pd.Timestamp("2022-02-22T11:49:00"),
            pd.Timestamp("2022-02-22T11:50:00"),
            pd.Timestamp("2022-02-22T11:51:00"),
        ],
        freq="min",
    )

    # Last 3 rows (original data) should have numeric index
    original_index = pd.RangeIndex(start=0, stop=3, step=1)

    # Combine indices
    combined_index = resampled_index.union(original_index, sort=False)
    expected_df.index = combined_index

    result_df = _prep_timeseries_data(
        charts=charts,
        timeseries_df=timeseries_df,
        start=start,
        end=end,
    )

    # Check that the qualified_name was mutated in the charts
    assert charts["chart1"]["targets"][0]["qualified_name"] == expected_qn

    # Verify the data content matches expected
    pd.testing.assert_frame_equal(
        result_df.sort_index(axis=1),
        expected_df.sort_index(axis=1),
        check_dtype=True,
        check_index_type=True,
        check_column_type=True,
        check_frame_type=True,
    )
