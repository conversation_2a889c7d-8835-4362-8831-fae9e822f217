import logging
from datetime import datetime
from typing import Any, cast

import utils_general

from .constants import STATIC_EVENT_COLORS
from .typings import Args
from .utils import subtract_days_from_iso_timestamp


def extract_event_args(event: dict[str, Any]) -> Any:
    input = utils_general.json_loads(event["body"])

    try:
        event_args = {
            "calc": input["calc"],
            "args": input["calc"]["args"],
            "currencies": input["calc"]["args"]["currencies"],
            "exchanges": input["calc"]["args"]["exchanges"],
            "models": input["calc"]["args"]["models"],
            "tenors": input["calc"]["args"]["plot_objects"]["timeseries"][
                "tenors"
            ],
            "plot_objects": input["calc"]["args"]["plot_objects"],
            "periods": int(input["calc"]["args"]["frequency"]["periods"]),
            "interval": input["calc"]["args"]["frequency"]["interval"],
            "output_version": input["calc"]["args"]["output_options"].get(
                "version", ""
            ),
        }
    except Exception as e:
        logging.exception(f"Error e={e} while processing event={event}")
        return

    return event_args


def populate_static_snapshots(
    calc_args: Args,
    target_snapshot: str,
    target_expiry: str,
    lookback_days: int,
) -> Args:

    one_year_ago_start = utils_general.to_iso(
        subtract_days_from_iso_timestamp(
            iso_timestamp=target_snapshot, days_to_subtract=365
        )
    )
    one_month_ago_start = utils_general.to_iso(
        subtract_days_from_iso_timestamp(
            iso_timestamp=target_snapshot, days_to_subtract=lookback_days
        )
    )
    one_week_ago_start = utils_general.to_iso(
        subtract_days_from_iso_timestamp(
            iso_timestamp=target_snapshot, days_to_subtract=7
        )
    )
    target_snapshot_hour_component = datetime.fromisoformat(
        one_week_ago_start.rstrip("Z")
    )
    formatted_time_target_snapshot = f"{target_snapshot_hour_component.hour:02}:{target_snapshot_hour_component.minute:02}"

    def replace_placeholders(obj: Args) -> Args:
        return cast(
            Args,
            utils_general.json_loads(
                utils_general.json_dumps(obj)
                # ordering matters as they share content
                .replace("_#ISO_START_ONE_MONTH_", one_month_ago_start)
                .replace("_#ISO_START_ONE_YEAR_", one_year_ago_start)
                .replace("_#ISO_START_ONE_WEEK_", one_week_ago_start)
                .replace("_#ISO_END_HOUR_", formatted_time_target_snapshot)
                .replace("_#ISO_END_", target_snapshot)
                .replace("#_LISTED_EXPIRY_TARGET_", target_expiry)
            ),
        )

    calc_args = replace_placeholders(obj=calc_args)
    calc_args = replace_placeholders_with_colors(calc_args=calc_args)

    return calc_args


def replace_placeholders_with_colors(calc_args: Args) -> Args:
    plot_objects_str = utils_general.json_dumps(calc_args)
    for placeholder, color in STATIC_EVENT_COLORS.items():
        plot_objects_str = plot_objects_str.replace(placeholder, color)

    return cast(Args, utils_general.json_loads(plot_objects_str))
