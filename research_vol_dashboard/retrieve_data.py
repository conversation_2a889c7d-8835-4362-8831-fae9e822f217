import asyncio
import logging
from concurrent.futures import Thread<PERSON>oolExecutor
from dataclasses import asdict
from typing import Any

import pandas as pd
import utils_general
from datagrabber import (
    CatalogAssetType,
    CatalogItem,
    GrabParams,
    LookupOptions,
    construct_timeseries_queries,
    get_instruments,
    grab,
)
from utils_calc import Model
from utils_general import IndexBackupQNsConfig

from scripts.bybit_perps import grab_historical_alt_perps, process_query_results

from .constants import (
    FILTER_MAPPING,
    QN_SUFFIX_MAPPING,
    QN_SUFFIX_MAPPING_TICK,
    UNVERSIONED_INSTRUMENTS,
)
from .typings import (
    AggregateInstrumentsChart,
    AggregationFilters,
    ExchangeData,
    GrabbedResult,
    InstrumentsGrab,
    PlotObjects,
    PlotTypes,
)
from .utils import is_lambda_context, subtract_days_from_iso_timestamp


def retrieve_data(
    start: str,
    end: str,
    currencies: list[str],
    plot_objects: PlotObjects,
    models: list[Model],
    periods: int,
    interval: str,
    version: str = "",
    consistent_read: bool = False,
    lookup_options: LookupOptions | None = None,
) -> dict[PlotTypes, GrabbedResult]:
    if not lookup_options:
        lookup_options = {}

    v = f"{version}." if version else ""
    frequency = f"{periods}{utils_general.INTERVAL_TO_LETTER[interval]}"

    # TODO: have a dedicated prep_data function to sort and parse through all the data
    # so we only need to perform one grab
    # plot_object -> plot_object_queries
    plot_objects_dict: dict[str, InstrumentsGrab] = (
        {}
    )  # todo: use pydatntic model to return grabparams and instruments for all items

    if "inversion_monitor" in plot_objects.keys():
        inversion_monitor_exchange = plot_objects["inversion_monitor"][
            "exchange"
        ]
        inversion_monitor_tenors = plot_objects["inversion_monitor"]["tenors"]
        inversion_monitor_tenors_qns: list[str] = [
            f"{v}{inversion_monitor_exchange}.option.{c}.{m}.{t}d.{frequency}.smile"
            for c in currencies
            for m in models
            for t in inversion_monitor_tenors
        ]

        volsurface_params_queries = construct_timeseries_queries(
            qualified_names=inversion_monitor_tenors_qns,
            start=int(utils_general.from_iso(start).timestamp() * 1e9),
            end=int(utils_general.from_iso(end).timestamp() * 1e9),
            fields=["qualified_name", "atm", "timestamp", "tenor_days"],
            consistent_read=consistent_read,
            lookup_options=lookup_options,
        )
        plot_objects_dict["inversion_monitor"] = InstrumentsGrab(
            grab_params=[*volsurface_params_queries]
        )

    if "v2timeseries" in plot_objects.keys():
        v2timeseries_queries = []

        for _chart_name, chart_details in plot_objects["v2timeseries"][
            "charts"
        ].items():
            # Track qualified names that need extra datapoint for resampling
            resample_qualified_names = set()

            query_start = start
            query_end = end
            # Allows us to fetch more historical data for a specific target e.g in the case
            # of a calculation where we need more data to compute a rolling window
            additional_lookback_days = chart_details.get(
                "additional_lookback_days"
            )
            if additional_lookback_days is not None:
                assert isinstance(additional_lookback_days, int)
                query_start = utils_general.to_iso(
                    subtract_days_from_iso_timestamp(
                        iso_timestamp=start,
                        days_to_subtract=additional_lookback_days,
                    )
                )

            # Group qualified names by whether they have sub_query_interval
            grouped_qualified_names = (
                []
            )  # For targets without sub_query_interval
            individual_targets = []  # For targets with sub_query_interval

            for target in chart_details["targets"]:
                qn = target["qualified_name"]
                if any(instr in qn for instr in UNVERSIONED_INSTRUMENTS):
                    qn = f"{qn}"
                else:
                    qn = f"{v}{qn}"

                # Check if target has resample_config and add to resample set
                resample_config = target.get("resample_config", None)
                if resample_config is not None and resample_config:
                    resample_qualified_names.add(qn)

                sub_query_interval = target.get("sub_query_interval", None)

                if sub_query_interval is None:
                    grouped_qualified_names.append(qn)
                else:
                    individual_targets.append((qn, sub_query_interval))

            # Process grouped qualified names (without sub_query_interval) in one call
            if grouped_qualified_names:
                unique_qualified_names = list(set(grouped_qualified_names))
                v2timeseries_queries.extend(
                    _generate_timeseries_queries(
                        qualified_names=unique_qualified_names,
                        start=query_start,
                        end=query_end,
                        consistent_read=consistent_read,
                        lookup_options=lookup_options,
                        sub_query_interval=None,
                    )
                )

            # Process individual targets with sub_query_interval
            for qn, sub_query_interval in individual_targets:
                v2timeseries_queries.extend(
                    _generate_timeseries_queries(
                        qualified_names=[qn],
                        start=query_start,
                        end=query_end,
                        consistent_read=consistent_read,
                        lookup_options=lookup_options,
                        sub_query_interval=sub_query_interval,
                    )
                )

            # Add extra query for qualified names with resample_config to fetch one datapoint before query_start
            if resample_qualified_names:
                resample_qualified_names_list = list(resample_qualified_names)
                v2timeseries_queries.extend(
                    construct_timeseries_queries(
                        qualified_names=resample_qualified_names_list,
                        end=int(
                            utils_general.from_iso(query_start).timestamp()
                            * 1e9
                        ),
                        fields=[],
                        consistent_read=consistent_read,
                        lookup_options={
                            "order_by": {"pkey": "asc", "skey": "desc"},
                            "limit": 1,
                        },
                    )
                )

        plot_objects_dict["v2timeseries"] = InstrumentsGrab(
            grab_params=[*v2timeseries_queries]
        )

    smile_constant_tenor_queries = []
    smile_listed_tenor_queries = []

    if "smiles" in plot_objects.keys():
        listed_expiry_exchange = plot_objects["smiles"][
            "listed_expiry_exchange"
        ]
        constant_tenor_exchange = plot_objects["smiles"][
            "constant_tenor_exchange"
        ]
        smile_models = plot_objects["smiles"]["models"]
        smile_constant_tenors_qns: list[str] = [
            f"{v}{constant_tenor_exchange}.option.{c}.{m}.{t}d.{frequency}.params"
            for c in currencies
            for m in smile_models
            for t in plot_objects["smiles"]["tenors"]
        ]

        smile_listed_tenors_qns: list[str] = [
            f"{v}{listed_expiry_exchange}.option.{c}.{m}.{frequency}.params"
            for c in currencies
            for m in plot_objects["smiles"]["models"]
        ]

        listed_expiry_snapshot = plot_objects["smiles"][
            "listed_expiry_timestamp"
        ]
        snapshot_smile_listed_tenor_queries = construct_timeseries_queries(
            qualified_names=smile_listed_tenors_qns,
            start=int(
                utils_general.from_iso(listed_expiry_snapshot).timestamp() * 1e9
            ),
            end=int(
                utils_general.from_iso(listed_expiry_snapshot).timestamp() * 1e9
            ),
            fields=[],
            consistent_read=consistent_read,
            lookup_options=lookup_options,
        )
        smile_listed_tenor_queries.extend(snapshot_smile_listed_tenor_queries)

        # iterate through the snapshot timestamps we want to collect
        for snapshot in plot_objects["smiles"]["snapshot_timestamps"]:
            snapshot_smile_constant_tenor_queries = (
                construct_timeseries_queries(
                    qualified_names=smile_constant_tenors_qns,
                    start=int(
                        utils_general.from_iso(snapshot).timestamp() * 1e9
                    ),
                    end=int(utils_general.from_iso(snapshot).timestamp() * 1e9),
                    fields=[],
                    consistent_read=consistent_read,
                    lookup_options=lookup_options,
                )
            )
            smile_constant_tenor_queries.extend(
                snapshot_smile_constant_tenor_queries
            )

        plot_objects_dict["smiles"] = InstrumentsGrab(
            grab_params=[
                *smile_constant_tenor_queries,
                *smile_listed_tenor_queries,
            ]
        )

    instruments_aggregator_qns: list[str] = []
    instrument_aggregator_instruments = []
    if "instruments_aggregator" in plot_objects:
        for _, chart_details in plot_objects["instruments_aggregator"][
            "charts"
        ].items():
            qn_instrument_list = _aggregate_instrument_grabber(
                chart_details=chart_details,
                start=start,
                end=end,
                frequency=frequency,
            )
            # NOTE: this function modifies chart_details and adds a qualified_names_key
            instruments_aggregator_qns.extend(
                item["qualified_name"] for item in qn_instrument_list
            )
            instrument_aggregator_instruments.extend(
                [item["instrument"] for item in qn_instrument_list]
            )

        plot_objects_dict["instruments_aggregator"] = InstrumentsGrab(
            grab_params=construct_timeseries_queries(
                qualified_names=list(set(instruments_aggregator_qns)),
                start=int(utils_general.from_iso(start).timestamp() * 1e9),
                end=int(utils_general.from_iso(end).timestamp() * 1e9),
                fields=[],
                consistent_read=consistent_read,
                lookup_options=lookup_options,
            ),
            instruments=instrument_aggregator_instruments,
        )

    if "v2smiles" in plot_objects.keys():
        for chart_name, _chart_details in plot_objects["v2smiles"][
            "charts"
        ].items():
            all_smile_queries = []
            for _target in _chart_details["targets"]:
                tenor = _target.get("tenor", None)
                listed_expiry = _target.get("listed_expiry", None)
                model = _target.get("model", None)
                currency = _target.get("currency", None)
                exchange = _target.get("exchange", None)
                iso_snap = _target.get("snapshot", None)

                assert iso_snap

                v2smile_new_listed_tenors_qns: list[str] = []
                v2smile_new_constant_tenors_qns: list[str] = []

                # only one will be available at a time
                if listed_expiry:
                    v2smile_new_listed_tenors_qns = [
                        f"{v}{exchange}.option.{currency}.{model}.listed.{frequency}.params"
                    ]
                elif tenor:
                    v2smile_new_constant_tenors_qns = [
                        f"{v}{exchange}.option.{currency}.{model}.{tenor}d.{frequency}.params"
                    ]

                snapshot_smile_queries = construct_timeseries_queries(
                    qualified_names=[
                        *v2smile_new_constant_tenors_qns,
                        *v2smile_new_listed_tenors_qns,
                    ],
                    start=int(
                        utils_general.from_iso(iso_snap).timestamp() * 1e9
                    ),
                    end=int(utils_general.from_iso(iso_snap).timestamp() * 1e9),
                    fields=[],
                    consistent_read=consistent_read,
                    lookup_options=lookup_options,
                )
                all_smile_queries.extend(snapshot_smile_queries)

            smile_queries = remove_list_duplicates(all_smile_queries)

            plot_objects_dict[chart_name] = InstrumentsGrab(
                grab_params=[*smile_queries]
            )

    term_structure_snapshot_queries = []

    if "volatility_term_structure" in plot_objects.keys():
        term_structure_exchange = plot_objects["volatility_term_structure"][
            "exchange"
        ]
        term_structure_snapshot_timestamps = plot_objects[
            "volatility_term_structure"
        ]["snapshot_timestamps"]
        term_structure_model = plot_objects["volatility_term_structure"][
            "model"
        ]
        term_structure_tenors_qns: list[str] = [
            f"{v}{term_structure_exchange}.option.{c}.{term_structure_model}.{t}d.{frequency}.smile"
            for c in currencies
            for t in plot_objects["volatility_term_structure"]["tenors"]
        ]
        for snapshot in term_structure_snapshot_timestamps:
            snapshot_term_structure_tenors_queries = (
                construct_timeseries_queries(
                    qualified_names=term_structure_tenors_qns,
                    start=int(
                        utils_general.from_iso(snapshot).timestamp() * 1e9
                    ),
                    end=int(utils_general.from_iso(snapshot).timestamp() * 1e9),
                    fields=["qualified_name", "atm", "timestamp", "tenor_days"],
                    consistent_read=consistent_read,
                    lookup_options=lookup_options,
                )
            )
            term_structure_snapshot_queries.extend(
                snapshot_term_structure_tenors_queries
            )

        plot_objects_dict["volatility_term_structure"] = InstrumentsGrab(
            grab_params=[*term_structure_snapshot_queries]
        )

    perpetual_qns: list[str] = []
    if "perpetual" in plot_objects.keys():
        perpetual_exchange = plot_objects["perpetual"]["exchange"]
        perpetual_base_qns: list[str] = [
            f"{perpetual_exchange}.perpetual.{c}-PERPETUAL.1h.funding.rate"
            for c in currencies
        ]
        perpetual_usdc_qns: list[str] = [
            f"{perpetual_exchange}.perpetual.{c}_USDC-PERPETUAL.1h.funding.rate"
            for c in currencies
        ]
        perpetual_qns.extend([*perpetual_usdc_qns, *perpetual_base_qns])

        perpetural_queries = construct_timeseries_queries(
            qualified_names=perpetual_qns,
            start=int(utils_general.from_iso(start).timestamp() * 1e9),
            end=int(utils_general.from_iso(end).timestamp() * 1e9),
            fields=[],
            consistent_read=consistent_read,
            lookup_options=lookup_options,
        )
        plot_objects_dict["perpetual"] = InstrumentsGrab(
            grab_params=[*perpetural_queries]
        )

    futures_term_structure_snapshot_queries = []
    if "futures_term_structure" in plot_objects.keys():
        futures_term_structure_exchange = plot_objects[
            "futures_term_structure"
        ]["exchange"]
        futures_term_structure_tenors = plot_objects["futures_term_structure"][
            "tenors"
        ]
        underlyings = plot_objects["futures_term_structure"]["underlyings"]

        futures_term_structure_snapshot_timestamps = plot_objects[
            "futures_term_structure"
        ]["snapshot_timestamps"]
        futures_term_structure_tenors_qns: list[str] = [
            f"{futures_term_structure_exchange}.future.{u}.{t}d.{frequency}.annual.pct"
            for u in underlyings
            for t in futures_term_structure_tenors
        ]
        for snapshot in futures_term_structure_snapshot_timestamps:
            futures_snapshot_term_structure_tenors_queries = (
                construct_timeseries_queries(
                    qualified_names=futures_term_structure_tenors_qns,
                    start=int(
                        utils_general.from_iso(snapshot).timestamp() * 1e9
                    ),
                    end=int(utils_general.from_iso(snapshot).timestamp() * 1e9),
                    fields=[],
                    consistent_read=consistent_read,
                    lookup_options=lookup_options,
                )
            )
            futures_term_structure_snapshot_queries.extend(
                futures_snapshot_term_structure_tenors_queries
            )

        plot_objects_dict["futures_term_structure"] = InstrumentsGrab(
            grab_params=[*futures_term_structure_snapshot_queries]
        )

    results = grab_objects(plot_objects_dict)

    # branch for Bybit alcoin query
    if "v2timeseries" in plot_objects.keys() and not is_lambda_context():
        chart_names = list(plot_objects["v2timeseries"]["charts"].keys())
        if any("perpetual" in chart_name.lower() for chart_name in chart_names):
            altcoin_timeseries_results = grab_historical_alt_perps(
                start_time=int(utils_general.from_iso(start).timestamp() * 1e3),
                end_time=int(utils_general.from_iso(end).timestamp() * 1e3),
                interval_time="1h",
            )
            altcoin_df = process_query_results(
                query_results=altcoin_timeseries_results, interval_time="1h"
            )
            altcoin_df["date"] = pd.to_datetime(
                altcoin_df["timestamp"], unit="ns", utc=True
            )
            altcoin_df.set_index("date", inplace=True)
            altcoin_df.sort_index(inplace=True)
            altcoin_df = altcoin_df.loc[start:end]
            altcoin_records = altcoin_df.to_dict(orient="records")
            grabbed_timeseries = results["v2timeseries"]
            grabbed_timeseries.data.extend(altcoin_records)
            results["v2timeseries"] = grabbed_timeseries

    return results


def grab_objects(
    plot_objects: dict[PlotTypes, InstrumentsGrab],
) -> dict[PlotTypes, GrabbedResult]:
    results: dict[PlotTypes, GrabbedResult] = {}
    for plot_object, instrument_grab in plot_objects.items():
        try:
            results[plot_object] = GrabbedResult(
                data=grab(instrument_grab.grab_params),
                instruments=instrument_grab.instruments,
            )
            logging.info(f"Successfully grabbed data, {plot_object=}")
        except Exception as ex:
            logging.error(f"Error grabbing data for {plot_object=}", ex)

    return results


async def grab_objects_async(
    plot_objects: dict[str, list[GrabParams]],
) -> dict[str, list[dict[str, object]]]:
    results: dict[str, list[dict[str, object]]] = {}
    loop = asyncio.get_event_loop()
    executor = ThreadPoolExecutor()

    async def run_grab(
        plot_object: str, object_queries: list[GrabParams]
    ) -> None:
        try:
            result = await loop.run_in_executor(executor, grab, object_queries)
            results[plot_object] = result
            logging.info(f"Successfully grabbed data, {plot_object=}")
        except Exception as ex:
            logging.error(
                f"Error grabbing data for {plot_object=}", exc_info=ex
            )

    tasks = [
        run_grab(plot_object, object_queries)
        for plot_object, object_queries in plot_objects.items()
    ]
    await asyncio.gather(*tasks)
    return results


def _aggregate_instrument_grabber(
    chart_details: AggregateInstrumentsChart,
    start: str,
    end: str,
    frequency: str,
) -> list[dict[str, CatalogItem | str]]:  # todo: update type:
    """
    Note: This function modifies chart_details
    """

    def _is_matching_instrument(
        instrument_details: CatalogItem, target: ExchangeData
    ) -> bool:
        return bool(
            target["original_quote_asset"] == instrument_details["quoteAsset"]
            and target["exchange"]
            == instrument_details["qualified_name"].split(".")[0]
        )

    def _filter_qualified_names(
        valid_qn_and_instruments: list[dict[str, CatalogItem | str]],
        chart_filters: AggregationFilters,
    ) -> list[dict[str, CatalogItem | str]]:
        """
        Further filtering based on specified chart filters
        """

        for filter_key in FILTER_MAPPING.keys():
            if filter_key in chart_filters.keys():
                if not filter_key == "exchanges":
                    filter_values = chart_filters[filter_key]
                else:
                    filter_values = [
                        _ef["exchange"] for _ef in chart_filters[filter_key]
                    ]

                valid_pairs = [
                    instr_pair
                    for instr_pair in valid_qn_and_instruments
                    if any(
                        fv in instr_pair["qualified_name"]
                        for fv in filter_values
                    )
                ]

        return valid_pairs

    instruments_query_result = []
    chart_filters = chart_details["filters"]
    for currency in chart_details["currencies"]:
        for exchange_data in chart_filters["exchanges"]:
            instruments_query_result.extend(
                get_instruments(
                    fields=[],
                    start=start,
                    end=end,
                    exchanges=[exchange_data["exchange"]],
                    asset_types=[chart_details["asset_type"]],
                    base_assets=[currency],
                    quote_assets=[exchange_data["original_quote_asset"]],
                )
            )

    valid_qn_and_instruments: list[dict[str, CatalogItem | str]] = []
    for instr in instruments_query_result:
        for exchange_data in chart_filters["exchanges"]:
            if _is_matching_instrument(
                instrument_details=instr, target=exchange_data
            ):
                qn = _create_instrument_qualified_name(
                    exchange=exchange_data["exchange"],
                    asset_type=chart_details["asset_type"],
                    frequency=frequency,
                    instrument=instr["instrument"],
                    target_suffix=chart_details["target_suffix"],
                )
                # used in prep_data
                instr["matching_instrument_qn"] = qn  # type: ignore
                valid_qn_and_instruments.append(
                    {
                        "qualified_name": qn,
                        "instrument": instr,
                    }
                )

    valid_qn_and_instruments = _filter_qualified_names(
        valid_qn_and_instruments=valid_qn_and_instruments,
        chart_filters=chart_filters,
    )

    if chart_details["chart_formats"]["dollar_denomination"]:
        for currency in chart_details["currencies"]:
            spot_catalog = utils_general.get_spot_with_backup_data(
                base=currency,
                frequency=frequency,
                quote="USD",  # todo: change oi and volume need to be quoted in the underlying
                config=IndexBackupQNsConfig(
                    use_mids=False,
                    number_per_ccy=1,
                ),
            )
            spot_catalog_dict = [asdict(item) for item in spot_catalog]
            valid_qn_and_instruments.extend(
                [
                    {
                        "qualified_name": catalog["qualified_name"],
                        "instrument": catalog,
                    }
                    for catalog in spot_catalog_dict
                ]
            )

    return valid_qn_and_instruments


def _create_instrument_qualified_name(
    exchange: str,
    asset_type: CatalogAssetType,
    target_suffix: str,
    frequency: str,
    instrument: str,
) -> str:
    if frequency != "tick":
        return f"{exchange}.{asset_type}.{instrument}.{frequency}.{QN_SUFFIX_MAPPING[target_suffix]}"
    else:
        return f"{exchange}.{asset_type}.{instrument}.tick.{QN_SUFFIX_MAPPING_TICK[target_suffix]}"


def _handle_plot_objects(plot_objects) -> None:
    # todo: finish
    # if "inversion_monitor" in plot_objects.keys():
    # if "v2timeseries" in plot_objects.keys():
    # if "smiles" in plot_objects.keys():
    # if "aggregate_instruments" in plot_objects.keys():
    # if "v2smiles" in plot_objects.keys():
    # if "volatility_term_structure" in plot_objects.keys():
    # if "perpetual" in plot_objects.keys():
    # if "futures_term_structure" in plot_objects.keys():

    pass


def _generate_timeseries_queries(
    qualified_names: list[str],
    start: str,
    end: str,
    consistent_read: bool,
    lookup_options: LookupOptions | None,
    fields: list[str] | None = None,
    sub_query_interval: int | None = None,
) -> list[GrabParams]:
    if fields is None:
        fields = []

    start_timestamp = int(utils_general.from_iso(start).timestamp() * 1e9)
    end_timestamp = int(utils_general.from_iso(end).timestamp() * 1e9)

    if not sub_query_interval:
        return construct_timeseries_queries(
            qualified_names=qualified_names,
            start=start_timestamp,
            end=end_timestamp,
            fields=fields,
            consistent_read=consistent_read,
            lookup_options=lookup_options,
        )

    sub_queries = []
    current_end = start_timestamp

    while current_end < end_timestamp:
        next_end = min(
            int(current_end + (sub_query_interval * 1e9)), end_timestamp
        )
        sub_queries.extend(
            construct_timeseries_queries(
                qualified_names=qualified_names,
                start=int(current_end),
                end=int(next_end),
                fields=fields,
                consistent_read=consistent_read,
                lookup_options={
                    "order_by": {"pkey": "asc", "skey": "desc"},
                    "limit": 1,
                },
            )
        )
        current_end = next_end
    return sub_queries


def remove_list_duplicates(dict_list: list[GrabParams]) -> list[GrabParams]:
    def make_hashable(obj: Any) -> Any:
        if isinstance(obj, dict):
            return frozenset(
                (key, make_hashable(value)) for key, value in obj.items()
            )
        elif isinstance(obj, list):
            return tuple(make_hashable(item) for item in obj)
        elif isinstance(obj, set):
            return frozenset(make_hashable(item) for item in obj)
        return obj

    seen = set()
    unique_list = []

    for d in dict_list:
        hashable_d = make_hashable(d)
        if hashable_d not in seen:
            seen.add(hashable_d)
            unique_list.append(d)

    return unique_list
