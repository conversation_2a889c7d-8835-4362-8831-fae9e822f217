import copy
import logging
import time
from typing import Literal

import pandas as pd
import utils_general
from datagrabber import CatalogItem
from utils_time_series import <PERSON>Shaper, ResampleConfig

from .typings import (
    FlexTimeseriesChart,
    GrabbedResult,
    PlotObjects,
    PlotTypes,
)
from .utils import generate_date_range


def prep_data(
    start: str,
    end: str,
    periods: int,
    interval: str,
    raw_data: dict[PlotTypes, GrabbedResult],
    plot_objects: PlotObjects,
) -> dict[str, pd.DataFrame]:
    prepped_result = {}
    for plot_type, grabbed_result in raw_data.items():
        plot_objects_df = pd.DataFrame(grabbed_result.data)
        if not plot_objects_df.empty:
            plot_objects_df["date"] = pd.to_datetime(
                plot_objects_df["timestamp"], unit="ns", utc=True
            )
            if plot_type != "smiles":
                plot_objects_df.set_index("date", inplace=True)
            if plot_type in [
                "timeseries",
                "monitor",
                "smiles",
                "term_structure",
                "skew",
            ]:
                if "tenor_days" in plot_objects_df.columns:
                    plot_objects_df.sort_values("tenor_days", inplace=True)

            if plot_type == "instruments_aggregator":
                plot_objects_df = _prep_instrument_aggregator(
                    start=start,
                    end=end,
                    periods=periods,
                    interval=interval,
                    df=plot_objects_df,
                    instruments=grabbed_result.instruments,
                )

            if plot_type == "v2timeseries":
                # qualified_names on the target objects, will be renamed if then we specify a resamples config
                plot_objects_df = _prep_timeseries_data(
                    charts=plot_objects["v2timeseries"]["charts"],
                    timeseries_df=plot_objects_df,
                    start=start,
                    end=end,
                )

        prepped_result[plot_type] = plot_objects_df

    return prepped_result


def _prep_timeseries_data(
    charts: dict[str, FlexTimeseriesChart],
    timeseries_df: pd.DataFrame,
    start: str,
    end: str,
) -> pd.DataFrame:
    """
    Processes and resamples timeseries data based on chart configurations.

    For each target in the provided charts configuration that contains a resample_config,
    the function applies the resampling using a base configuration merged with the target's
    specific settings. The function modifies the underlying plot objects by updating the
    'qualified_name' field with a suffix indicating the resampling parameters.

    Parameters:
        charts (dict[str, FlexTimeseriesChart]): Chart configuration dictionary. Note that
            this dictionary will be modified in-place as the 'qualified_name' of targets are updated.
        timeseries_df (pd.DataFrame): DataFrame containing the timeseries data.
        start (str): Start date for the resampling period.
        end (str): End date for the resampling period.

    Returns:
        pd.DataFrame: Combined DataFrame including both the resampled series and the original timeseries data.
    """

    resampled_dfs = []
    pick_cfg_base: ResampleConfig = {
        "interval": "hour",  # will be updated by specific config
        "periods": 1,  # will be updated by series specifiec config
        "index_field": "timestamp",
        "methods": {
            "fill": {
                "methods": {"ffill": ""},
                "date_range": {
                    "absolute": {
                        "start": start,
                        "end": end,
                    }
                },
            },
            "pick_first": {},
        },
    }

    for _name, chart_items in charts.items():
        for target in chart_items["targets"]:
            resample_config = target.get("resample_config", None)
            if resample_config is None or not resample_config:
                continue

            pick_cfg_base.update(resample_config)  # type: ignore
            interval = resample_config["interval"]
            periods = resample_config["periods"]

            # we mutate the qn as the underlying data for a qualified_name is shared across multiple charts
            # so we do not want to mutate the original data.
            qn_suffix = f".{periods}_{interval}"

            qn_df = (
                timeseries_df[
                    timeseries_df["qualified_name"].str.contains(
                        f"{target['qualified_name']}"
                    )
                ]
                .sort_values("timestamp")
                .copy()
            )

            if qn_df.empty:
                continue

            start_timestamp_ns = int(
                utils_general.from_iso(start).timestamp() * 1e9
            )
            end_timestamp_ns = int(
                utils_general.from_iso(end).timestamp() * 1e9
            )
            if qn_df["timestamp"].min() < start_timestamp_ns:
                # The current implementation of the DataShaper cannot forward fill ticks before the start date when
                # the input dataframe contains data before the start date and data after the start date. It can however
                # handle dataframes that only have data before the start date. Therefore, we split the shaping process
                # into two parts. The first part shapes the data before the start date and up to and including the start date.
                # The resulting dataframe is guaranteed to have data on the exact "start_date".
                # The second part shapes the data after the start date, but before we do this, we include the last datapoint from part 1.
                # so that we guarantee that the dataframe that is input has a datapoint on the start date and we can
                # forward fill from there. We then concatenate the two dataframes together.

                # Configure and process data up to start date
                pre_start_config = copy.deepcopy(pick_cfg_base)
                pre_start_config["methods"]["fill"]["date_range"]["absolute"][
                    "start"
                ] = utils_general.to_iso(qn_df["timestamp"].min())
                pre_start_config["methods"]["fill"]["date_range"]["absolute"][
                    "end"
                ] = start

                pre_start_data = DataShaper.shape(
                    [[{"resample": pre_start_config}]], qn_df
                )[0][0]

                if pre_start_data.empty:
                    raise ValueError(
                        f"Empty dataframe after shaping pre-start data "
                        f"{pre_start_config=}"
                    )

                # Add boundary data point to ensure continuity
                start_boundary_point = pre_start_data[
                    pre_start_data["timestamp"] == start_timestamp_ns
                ]
                df_with_start_boundary = pd.concat(
                    [start_boundary_point, qn_df]
                ).drop_duplicates(
                    subset=["timestamp", "qualified_name"], keep="last"
                )  # keep last here so we prioritize the original qn_df

                # Process main data range
                post_start_data = DataShaper.shape(
                    [[{"resample": pick_cfg_base}]], df_with_start_boundary
                )[0][0]

                # Combine and filter results
                combined_data = pd.concat(
                    [pre_start_data, post_start_data]
                ).drop_duplicates(
                    subset=["timestamp", "qualified_name"], keep="last"
                )

                resampled_df = combined_data[
                    (combined_data["timestamp"] >= start_timestamp_ns)
                    & (combined_data["timestamp"] <= end_timestamp_ns)
                ]

            else:
                resampled_df = DataShaper.shape(
                    [[{"resample": pick_cfg_base}]], qn_df
                )[0][0]

            # modify the name in the calc_objects as well as the timeseries
            modified_qn = f"{target['qualified_name']}{qn_suffix}"
            target["qualified_name"] = modified_qn
            resampled_df["qualified_name"] = modified_qn
            resampled_dfs.append(resampled_df)

    if resampled_dfs:
        df_with_resampled_series = pd.concat(
            [*resampled_dfs, timeseries_df], copy=False, sort=False
        )
    else:
        df_with_resampled_series = timeseries_df

    return df_with_resampled_series


def _prep_instrument_aggregator(
    start: str,
    end: str,
    periods: int,
    interval: str,
    df: pd.DataFrame,
    instruments: list[CatalogItem],
) -> pd.DataFrame:
    # todo: Please Note that this removes the quote currency from the instruments.

    instruments_dict = [
        dict(t) for t in {tuple(d.items()) for d in instruments}
    ]

    for item in instruments_dict:
        # todo: move to appropriate locations
        item["contract_type"] = _determine_contract_type(  # type: ignore
            base_asset=item.get("baseAsset", item.get("base", "")),
            quote_asset=item.get("quoteAsset", item.get("quote", "")),
            settlement_asset=item.get("settlementAsset", ""),
            vol_currency=item.get("volCurrency", ""),
        )
        if "contractSize" not in item:
            item["contractSize"] = 1.0

    lookup_dict = {
        d.get("matching_instrument_qn") or d["qualified_name"]: d
        for d in instruments_dict
    }
    df["catalog"] = df["qualified_name"].map(lookup_dict)

    # will need to modify if we want cross quote currency comparison
    df["asset_type"] = df["qualified_name"].str.split(".", expand=True)[1]

    df["exchange"] = ""
    df["currency"] = ""
    df["expiry"] = ""
    df["strike"] = ""
    df["option_type"] = ""
    df["instrument"] = ""

    time_of_start = time.time()
    df = _clean_spot_data(
        start=start, end=end, df=df, periods=periods, interval=interval
    )
    logging.info(
        f"Spot data cleaning took {time.time() - time_of_start:.2f}s",
    )

    # Masks for different asset types
    option_mask = df["asset_type"] == "option"
    future_mask = df["asset_type"] == "future"
    perpetual_mask = df["asset_type"] == "perpetual"

    time_of_start = time.time()
    # Processing options
    if not df.loc[option_mask].empty:
        df = _process_options(df, option_mask)
    logging.info(
        f"Options processing took {time.time() - time_of_start:.2f}s",
    )

    time_of_start = time.time()

    # Processing futures
    if not df.loc[future_mask].empty:
        df = _process_futures(df, future_mask)
    logging.info(
        f"Futures processing took{time.time() - time_of_start:.2f}s",
    )

    time_of_start = time.time()
    # Processing perpetuals
    if not df.loc[perpetual_mask].empty:
        df = _process_perpetuals(df, perpetual_mask)
    logging.info(
        f"Perpetuals processing took {time.time() - time_of_start:.2f}s",
    )

    return df


def _process_options(
    df: pd.DataFrame, option_mask: "pd.Series[bool]"
) -> pd.DataFrame:
    fields_to_apply = {
        "contract_type": "contract_type",
        "contractSize": "contractSize",
        "expiry": "expiry",
        "option_type": "type",
        "strike": "strike",
    }

    split_df_option = df.loc[option_mask, "qualified_name"].str.split(
        r"[.-]", expand=True
    )
    currency_split_option = split_df_option[2].str.split("_", expand=True)

    _apply_catalog_fields(df, option_mask, fields_to_apply)

    df.loc[option_mask, "exchange"] = split_df_option[0]
    df.loc[option_mask, "currency"] = currency_split_option[0]

    df.loc[option_mask, "instrument"] = _create_normalized_instrument(
        df=df,
        mask=option_mask,
        ordered_components=["currency", "expiry", "strike", "option_type"],
    )

    return df


def _process_futures(
    df: pd.DataFrame, future_mask: "pd.Series[bool]"
) -> pd.DataFrame:
    fields_to_apply = {
        "contract_type": "contract_type",
        "contractSize": "contractSize",
        "expiry": "expiry",
    }

    split_df_future = df.loc[future_mask, "qualified_name"].str.split(
        r"[.-]", expand=True
    )

    if split_df_future.shape[1] > 2:  # Check if there are enough columns
        currency_split_future = split_df_future[2].str.split("_", expand=True)

        _apply_catalog_fields(df, future_mask, fields_to_apply)

        df.loc[future_mask, "exchange"] = split_df_future[0]
        df.loc[future_mask, "currency"] = currency_split_future[0]
        df.loc[future_mask, "instrument"] = _create_normalized_instrument(
            df=df, mask=future_mask, ordered_components=["currency", "expiry"]
        )

    return df


def _process_perpetuals(
    df: pd.DataFrame, perpetual_mask: "pd.Series[bool]"
) -> pd.DataFrame:
    fields_to_apply = {
        "contract_type": "contract_type",
        "contractSize": "contractSize",
        "currency": "baseAsset",
    }

    split_df_perpetual = df.loc[perpetual_mask, "qualified_name"].str.split(
        ".", expand=True
    )

    # todo - update the use of the dangerous function
    df.loc[perpetual_mask, "currency"] = df.loc[
        perpetual_mask, "catalog"
    ].apply(lambda x: x["baseAsset"])

    _apply_catalog_fields(df, perpetual_mask, fields_to_apply)

    df.loc[perpetual_mask, "exchange"] = split_df_perpetual[0]
    df.loc[perpetual_mask, "type"] = split_df_perpetual[1]
    df.loc[perpetual_mask, "instrument"] = split_df_perpetual[2]

    return df


def _clean_spot_data(
    start: str, end: str, periods: int, interval: str, df: pd.DataFrame
) -> pd.DataFrame:
    spot_df = df[df["asset_type"] == "spot"].copy()
    spot_df.loc[:, "currency"] = spot_df["qualified_name"].str.extract(
        r"\.(\w+)_USD\.", expand=False
    )

    frequency = f"{periods}{utils_general.INTERVAL_TO_PANDAS_ALIAS[interval]}"
    complete_index = generate_date_range(
        start_date=start, end_date=end, freq=frequency
    )

    filled_dfs = []

    for currency, group in spot_df.groupby("currency"):
        reindexed_group = group.reindex(complete_index)
        reindexed_group["ffilled"] = reindexed_group["px"].isna()
        filled_group = reindexed_group.ffill()
        filled_group["currency"] = currency
        filled_group["timestamp"] = filled_group.index.astype("int64")
        filled_group = filled_group[filled_group["ffilled"]]
        filled_dfs.append(filled_group)

    # Concatenate all filled DataFrames
    filled_df = pd.concat(filled_dfs) if filled_dfs else None
    return pd.concat([df, filled_df])


ContractType = Literal["inverse", "linear"]


def _determine_contract_type(
    base_asset: str,
    quote_asset: str,
    settlement_asset: str | None = None,
    vol_currency: str | None = None,
) -> ContractType | None:
    """
    If no settlement or vol currency provided, assumes its a spot instrument. Otherwise,
    determines if a contract is linear or inverse based on the base asset, quote asset,
    settlement asset, and volume currency.

    - Linear contract: Settlement asset == Quote asset
    - Inverse contract: Settlement asset == Base asset
    - If volCurrency matches base or quote, it can act as an additional clue
    """

    # Rule 1: If not settlement and vol_currency at this point means its spot
    if not settlement_asset and not vol_currency:
        return None
    # Rule 2: If settlement asset is the same as the quote asset, it's a linear contract
    if settlement_asset == quote_asset:
        return "linear"

    # Rule 3: If settlement asset is the same as the base asset, it's an inverse contract
    elif settlement_asset == base_asset:
        return "inverse"

    # Rule 4: If none of the above, check volume currency for additional hint
    elif vol_currency == base_asset:
        return "inverse"

    elif vol_currency == quote_asset:
        return "linear"
    else:
        raise NotImplementedError(
            f"Unable to determine contract Type {base_asset=} {quote_asset=} {settlement_asset=} {vol_currency=}"
        )


def _create_normalized_instrument(
    df: pd.DataFrame, mask: "pd.Series[bool]", ordered_components: list[str]
) -> "pd.Series[str]":
    """
    Creates a normalized instrument field based on the given components.

    Parameters:
    - df: The DataFrame containing the relevant columns.
    - mask: The boolean mask to filter rows where the instrument should be created.
    - ordered_components: A list of column names to be combined into the instrument string.

    Returns:
    - A pandas Series containing the normalized instrument values.
    """
    # Ensure all components exist in the DataFrame
    for component in ordered_components:
        if component not in df.columns:
            raise ValueError(f"Component '{component}' not found in DataFrame")

    # Construct the instrument string by joining the specified components with "-"
    return df.loc[mask, ordered_components].astype(str).agg("_".join, axis=1)


def _apply_catalog_fields(
    df: pd.DataFrame, mask: "pd.Series[bool]", fields: dict[str, str]
) -> None:
    """
    Helper function to apply catalog fields to a DataFrame based on a mask.

    Parameters:
    - df: The DataFrame to update.
    - mask: The mask to filter rows where the fields should be updated.
    - fields: A dictionary where keys are DataFrame columns and values are catalog keys.
    """
    for df_column, catalog_key in fields.items():
        df.loc[mask, df_column] = df.loc[mask, "catalog"].apply(
            lambda x, key=catalog_key: x[key]
        )
