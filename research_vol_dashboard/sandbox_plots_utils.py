import logging
import math
from datetime import datetime
from typing import Any, cast

import numpy as np
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import utils_calc
import utils_general
from plotly.subplots import make_subplots
from utils_calc import Model

from .constants import (
    MONEYNESS_DENSE,
)
from .typings import (
    ChartType,
    FlexTimeSeriesTarget,
    NDArrayFloat64,
    VolSpace,
    XValues,
    YValues,
)
from .utils import (
    _model_vol,
    is_lambda_context,
    round_down,
    round_up,
    save_image,
)
from .utils_plot import (
    abbreviate_number,
    bs_flex_smile_layout,
    bs_flex_timeseries_layout,
    expand_y_range,
    generate_tickvals,
    get_y_scale,
    update_fig_layout,
)


def flex_smile_plots(
    data_df: pd.DataFrame,
    vol_space: VolSpace,
    yaxis_title: str,
    chart_name: str,
    chart_title: str,
) -> go.Figure:
    def _vol_space_plotter(
        figure: go.Figure,
        vols: list[float] | NDArrayFloat64,
        trace_details: dict[str, Any],
        trace_target: str,
        x_vals: list[float] | NDArrayFloat64 | None = None,
    ) -> go.Figure:
        if x_vals is None:
            x_vals = MONEYNESS_DENSE

        if vol_space == "moneyness":
            figure.add_trace(
                go.Scatter(
                    x=x_vals,
                    y=vols,
                    name=trace_details[trace_target]["trace_title"],
                    line={"color": trace_details[trace_target]["color"]},
                    mode="lines",
                )
            )

        elif vol_space == "delta":
            figure.add_trace(
                go.Scatter(
                    x=x_vals,
                    y=vols,
                    name=trace_details[trace_target]["trace_title"],
                    line={"color": trace_details[trace_target]["color"]},
                    mode="lines",
                )
            )

        return figure

    def _iso_expiry_to_underlying_index(underlying_index: str) -> str:
        expiry_datetime = datetime.fromisoformat(underlying_index.rstrip("Z"))
        formatted_date = expiry_datetime.strftime("%d%b%y").upper()
        return formatted_date

    def condition(x: float) -> bool:
        return 0.0001 <= x <= 0.9999

    def _get_upper_y_lim(
        y_lims: list[float], vols: list[float] | NDArrayFloat64
    ) -> list[float]:
        # to get the upper ylim of the charts, we want to find the max vols between the
        # 0.8 and 1.2 moneyness
        target_moneyness = [0.8, 1.2]
        for m in target_moneyness:
            # Find the closest strike to the target moneyness
            idx_closest = (np.abs(MONEYNESS_DENSE - m)).argmin()
            y_lims.append(vols[idx_closest])

        return y_lims

    y_lim_candidates: list[float] = []
    min_vol = math.inf
    max_vol = 0

    fig = px.line()

    listed_params_df = data_df[data_df["qualified_name"].str.contains("listed")]
    constant_tenor_df = data_df[
        ~data_df["qualified_name"].str.contains("listed")
    ]

    for _idx, smile in constant_tenor_df.iterrows():
        version, qn_tokens = utils_general.get_qfn_and_version(
            smile["qualified_name"]
        )
        model = cast(Model, qn_tokens[3])
        trace_details = smile["trace_details"]

        # todo: switch strikes and vols with mode
        strikes = MONEYNESS_DENSE * smile["forward"]
        vols = np.asarray(_model_vol(smile, model, np.asarray(strikes)))

        if vol_space == "delta":
            put_deltas = [
                (
                    1
                    - utils_calc.option_delta(
                        s=smile["spot"],
                        f=smile["forward"],
                        K=strike,
                        t=smile["expiry"],
                        r_d=0,
                        vol=vol,
                        phi=1,
                    )
                )
                for strike, vol in zip(strikes, vols, strict=False)
            ]
            filtered_pairs = [
                (x, y)
                for x, y in zip(put_deltas, vols, strict=False)
                if condition(x)
            ]
            put_deltas, vols = (
                [list(t) for t in zip(*filtered_pairs, strict=False)]
                if filtered_pairs
                else ([], [])
            )
        else:
            put_deltas = None

        fig = _vol_space_plotter(
            figure=fig,
            vols=vols,
            trace_details=trace_details,
            trace_target=smile["tenor_days"],
            x_vals=put_deltas,
        )

        y_lim_candidates = _get_upper_y_lim(y_lim_candidates, vols)

        if min(vols) < min_vol:
            min_vol = min(vols)
        if max(vols) > max_vol:
            max_vol = max(vols)

    # add constant_tenor plots add listed_expiry plots
    for _idx, smile in listed_params_df.iterrows():
        version, qn_tokens = utils_general.get_qfn_and_version(
            smile["qualified_name"]
        )
        params_df = pd.DataFrame(smile["params"])
        params_df["qualified_name"] = smile["qualified_name"]
        params_df["underlying_index"] = params_df["expiry_str"].apply(
            _iso_expiry_to_underlying_index
        )
        expiries_to_plot = smile["target_expiries"]
        model = cast(Model, qn_tokens[3])
        trace_details = smile["trace_details"]

        for target_expiry in expiries_to_plot:
            try:
                target_smile = params_df[
                    params_df["underlying_index"] == target_expiry
                ].iloc[0]

                # todo: switch strikes and vols with model
                # vols = _model_vol(
                #     target_smile, model, MONEYNESS_DENSE * target_smile["forward"]
                # )

                # strikes = _vol_space_to_strikes(target_smile)
                strikes = MONEYNESS_DENSE * target_smile["forward"]
                vols = _model_vol(target_smile, model, np.asarray(strikes))

                if vol_space == "delta":
                    put_deltas = [
                        (
                            1
                            - utils_calc.option_delta(
                                s=target_smile["spot"],
                                f=target_smile["forward"],
                                K=strike,
                                t=target_smile["expiry"],
                                r_d=0,
                                vol=vol,
                                phi=1,
                            )
                        )
                        for strike, vol in zip(strikes, vols, strict=False)
                    ]
                    filtered_pairs = [
                        (x, y)
                        for x, y in zip(put_deltas, vols, strict=False)
                        if condition(x)
                    ]
                    put_deltas, vols = (
                        [list(t) for t in zip(*filtered_pairs, strict=False)]
                        if filtered_pairs
                        else ([], np.array([]))
                    )
                else:
                    put_deltas = None

                fig = _vol_space_plotter(
                    figure=fig,
                    vols=vols,
                    trace_details=trace_details,
                    trace_target=target_expiry,
                    x_vals=put_deltas,
                )

                y_lim_candidates = _get_upper_y_lim(y_lim_candidates, vols)

                if min(vols) < min_vol:
                    min_vol = min(vols)
                if max(vols) > max_vol:
                    max_vol = max(vols)

            except Exception:
                logging.exception(
                    f"Error plotting smiles. {target_expiry=}, {model=}, {vol_space=}"
                )

    min_money = 0.5
    max_money = 1.5

    fig = bs_flex_smile_layout(
        figure=fig,
        y_axis_range=(
            round_down(min_vol - 0.05, 0.05),
            round_up(max(y_lim_candidates) + 0.25, 0.05),
        ),
        x_axis_range=(
            (min_money, max_money) if vol_space == "moneyness" else (0, 1.0)
        ),
        y_title=yaxis_title,
        chart_title=chart_title,
        y_axis_tick_mult=100,
        x_axis_tick_mult=1,
        y_axis_ticks_suffix="%",
        x_axis_tick_suffix="",
        x_axis_steps=10,
        domain=vol_space,
    )
    filename = f"{chart_name}.png"
    if not is_lambda_context():
        save_image(file_name=filename, figure=fig)
    return fig


def make_flex_calc_timeseries_fig(
    data_df: "pd.Series[float]",
    chart_name: str,
    yaxis_title: str,
    chart_title: str,
    yaxis_tick_size: int,
) -> go.Figure:
    fig = make_subplots(specs=[[{"secondary_y": True}]])
    min_val, max_val = data_df.min(), data_df.max()

    fig.add_trace(go.Scatter(mode="lines", x=data_df.index, y=data_df.values))
    tickvals = list(
        range(math.floor(min_val), math.ceil(max_val) + 1, yaxis_tick_size)
    )
    fig = update_fig_layout(fig, chart_title, yaxis_title, tickvals)

    filename = f"{chart_name}.png"
    if not is_lambda_context():
        save_image(file_name=filename, figure=fig)
    return fig


def make_flex_timeseries_fig(
    data_df: pd.DataFrame,
    target_to_plot: list[FlexTimeSeriesTarget],
    chart_name: str,
    yaxis_title: str,
    chart_type: ChartType,
    chart_title: str,
    tickprefix: str | None,
    ticksuffix: str | None,
) -> go.Figure:

    # this shouldnt happen in the current use case. Update we need it
    assert not (tickprefix and ticksuffix)

    if "perpetual" in chart_name.lower() and "bybit" in chart_name.lower():
        chart_name_og = ""
    else:
        chart_name_og = chart_name

    fig = create_chart(chart_type=chart_type, title=chart_name_og)

    min_val: float | int
    max_val: float | int
    min_val, max_val = math.inf, 0

    for chart_target in target_to_plot:
        qn = chart_target.get("qualified_name")
        trace_title = chart_target.get("trace_title")

        assert qn, f"qualified_name is not defined for {chart_target=}"

        color = chart_target.get("color")
        series_to_plot = chart_target["target"]
        timeseries_data = data_df[data_df["qualified_name"].str.contains(qn)]

        if timeseries_data.empty:
            continue

        timeseries_data.sort_values("timestamp", inplace=True)

        x_values = timeseries_data.index
        if "index" in chart_target["qualified_name"] or any(
            suf in chart_target["qualified_name"] for suf in [".px", ".params"]
        ):

            y_values = timeseries_data[series_to_plot].astype(float)
        else:
            # suffixes like .rate .curve .pct .smile .skew
            y_values = timeseries_data[series_to_plot].astype(float) * 100

        show_legend = True

        if "perpetual" in chart_name.lower() and "bybit" in chart_name.lower():
            show_legend = False
            trace_title = ""
            funding_hours = [0, 8, 16]
            mask = timeseries_data.index.hour.isin(funding_hours)
            filtered_data = timeseries_data[mask]
            x_values = filtered_data.index
            y_values = filtered_data[series_to_plot].astype(float) * 100

        add_trace(
            chart_type=chart_type,
            fig=fig,
            name=trace_title,
            x_values=x_values,
            y_values=y_values,
            show_legend=show_legend,
            inverted=False,
            color=color,
        )

        min_timeseries_val, max_timeseries_val = float(y_values.min()), float(
            y_values.max()
        )
        min_val = (
            min_timeseries_val if min_timeseries_val < min_val else min_val
        )
        max_val = (
            max_timeseries_val if max_timeseries_val > max_val else max_val
        )

    if "perpetual" in chart_name.lower() and "bybit" in chart_name.lower():
        fig = bs_flex_timeseries_layout(
            figure=fig,
            y_axis_range=(min_val, max_val),
            num_y_ticks=5,
            y_tick_suffix="%",
        )
        fig.update_layout(height=181 * 2, width=1692 * 2)
        fig.update_layout(
            yaxis={"range": [-0.01, 0.04]},
            font={"size": 40, "color": "white"},
        )
    else:
        diff = max_val - min_val
        if diff == 0:
            y_scale = get_y_scale(max_val)
        else:
            y_scale = get_y_scale(diff)

        tickvals = generate_tickvals(
            min_val=round_down(min_val, y_scale),
            max_val=round_up(max_val, y_scale),
        )

        chart_title = ""

        # Assumes prefix and suffix cannot be specifietat the same time
        ticktext = None
        if tickprefix is not None:
            ticktext = [
                f"{tickprefix}{abbreviate_number(val=i, y_scale=y_scale)}"
                for i in tickvals
            ]
        elif ticksuffix is not None:
            ticktext = [
                f"{abbreviate_number(val=i, y_scale=y_scale)}{ticksuffix}"
                for i in tickvals
            ]

        tickvals_list = tickvals.tolist()
        fig = update_fig_layout(
            fig=fig,
            chart_title=chart_title,
            yaxis_title=yaxis_title,
            tickvals=tickvals_list,
            ticktext=ticktext,
            y_range=expand_y_range(
                tickvals=tickvals_list,
                min_val=min_val,
                max_val=max_val,
                y_scale=y_scale / 10,
            ),
        )

    filename = f"{chart_name}.png"
    if not is_lambda_context():
        save_image(file_name=filename, figure=fig)
    return fig


def add_timeseries_trace(
    fig: go.Figure,
    x_values: XValues,
    y_values: YValues,
    name: str | None = None,
    show_legend: bool | None = True,
    color: str | None = None,
    scaler: float | None = 1,
) -> go.Figure:
    fig.add_trace(
        go.Scatter(
            mode="lines",
            x=x_values,
            y=y_values,
            name=name,
            line={"color": color},
            showlegend=show_legend,
        )
    )


def create_bar_figure(title: str) -> go.Figure:
    fig = go.Figure()
    fig.update_layout(
        barmode="stack",
        title=title,
    )
    return fig


def add_bar_trace(
    fig: go.Figure,
    x_values: XValues,
    y_values: YValues,
    name: str | None,
    show_legend: bool | None = True,
    inverted: bool | None = False,
    color: str | None = None,
) -> go.Figure:
    y_values = np.asarray(y_values) * (-1 if inverted else 1)
    fig.add_trace(
        go.Bar(
            # name=name if (show_legend and name) else None,
            name=name,
            x=x_values,
            y=y_values,
            marker_color=color,
            showlegend=show_legend,
        )
    )
    if inverted:
        fig.update_layout(barmode="relative")
    return fig


def create_stacked_area_figure(title: str) -> go.Figure:
    fig = go.Figure()
    fig.update_layout(
        title=title,
    )
    return fig


def add_stacked_area_trace(
    fig: go.Figure,
    x_values: XValues,
    y_values: YValues,
    name: str | None,
    show_legend: bool | None = True,
    inverted: bool | None = False,
    color: str | None = None,
) -> go.Figure:
    stackgroup = "negative" if inverted else "positive"
    y_values = np.asarray(y_values) * (-1 if inverted else 1)
    fig.add_trace(
        go.Scatter(
            x=x_values,
            y=y_values,
            mode="lines",
            name=name if (show_legend and name) else None,
            line={"width": 0.5, "color": color},
            stackgroup=stackgroup,
            showlegend=show_legend,
        )
    )
    return fig


def create_timeseries_figure(title: str) -> go.Figure:
    fig = go.Figure()
    fig.update_layout(
        title=title,
    )
    return fig


def create_chart(chart_type: ChartType, title: str) -> go.Figure:
    if chart_type == "bar":
        return create_bar_figure(title)
    elif chart_type == "stacked_area":
        return create_stacked_area_figure(title)
    elif chart_type == "timeseries":
        return create_timeseries_figure(title)
    else:
        raise ValueError(f"Invalid chart type specified, {chart_type}")


def add_trace(
    chart_type: ChartType,
    fig: go.Figure,
    x_values: XValues,
    y_values: YValues,
    name: str | None,
    show_legend: bool | None = True,
    inverted: bool | None = False,
    color: str | None = None,
    scaler: float | None = 1,
) -> go.Figure:
    if chart_type == "bar":
        return add_bar_trace(
            fig=fig,
            name=name,
            x_values=x_values,
            y_values=y_values,
            show_legend=show_legend,
            inverted=inverted,
            color=color,
        )
    elif chart_type == "stacked_area":
        return add_stacked_area_trace(
            fig=fig,
            x_values=x_values,
            y_values=y_values,
            name=name,
            show_legend=show_legend,
            inverted=inverted,
            color=color,
        )
    elif chart_type == "timeseries":
        return add_timeseries_trace(
            fig=fig,
            x_values=x_values,
            y_values=y_values,
            name=name,
            show_legend=show_legend,
            color=color,
        )
    else:
        raise ValueError("Invalid chart type specified")
