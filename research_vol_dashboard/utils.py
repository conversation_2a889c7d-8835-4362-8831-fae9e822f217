import base64
import logging
import math
import os
import time
from collections.abc import Callable
from datetime import UTC, datetime, timedelta
from pathlib import Path
from typing import Any

import pandas as pd
import plotly.graph_objects as go
import utils_calc
import utils_general
from utils_calc import Model

from research_vol_dashboard.constants import LAMBDA_CONTEXT_ENV_NAME
from research_vol_dashboard.typings import NDArrayFloat64


def format_unix_timestamp_ns(unix_timestamp_ns: int) -> str:
    unix_timestamp_s = unix_timestamp_ns / 1e9
    date = datetime.fromtimestamp(unix_timestamp_s, tz=UTC)
    month_year = date.strftime("%b")
    day = date.day
    if 4 <= day <= 20 or 24 <= day <= 30:
        suffix = "th"
    else:
        suffix = ["st", "nd", "rd"][day % 10 - 1]

    return f"{day}{suffix} {month_year}"


def round_up(n: float, round_to_nearest: float) -> float:
    """Rounds a number up to the nearest specified value."""
    return math.ceil(n / round_to_nearest) * round_to_nearest


def round_down(n: float, round_to_nearest: float) -> float:
    """Rounds a number down to the nearest specified value."""
    return math.floor(n / round_to_nearest) * round_to_nearest


def _model_vol(
    row: "pd.Series[float]",
    model: Model,
    strike: float | NDArrayFloat64,
) -> float | NDArrayFloat64:
    vol: float | NDArrayFloat64
    forward = row["forward"]
    expiry = row["expiry"]

    if model == "SABR":
        vol = utils_calc.sabr_vol(
            strike,
            forward,
            expiry,
            row["sabr_alpha"],
            1,
            row["sabr_rho"],
            row["sabr_volvol"],
        )
        vol = vol.item() if vol.size == 1 else vol
    elif model == "SVI":
        vol = utils_calc.svi_vol(
            row["svi_a"],
            row["svi_b"],
            row["svi_rho"],
            row["svi_m"],
            row["svi_sigma"],
            forward,
            expiry,
            strike,
        )
    else:
        raise NotImplementedError("Unrecognised model")

    return vol


def subtract_days_from_iso_timestamp(
    iso_timestamp: str, days_to_subtract: int
) -> datetime:
    # Parse the ISO timestamp into a datetime object
    dt = utils_general.from_iso(iso_timestamp)

    # Subtract the specified number of days
    modified_dt = dt - timedelta(days=days_to_subtract)

    # Return the modified datetime as an ISO-formatted string
    return modified_dt


# Adjust the match_data_with_event function to be generic and dynamic
def match_data_with_event(
    data_row: "pd.Series[Any]",
    currency: str,
    model: str,
    exchange: str,
    target: str | int,
) -> bool:
    # Parse qualified_name
    version, qn_tokens = utils_general.get_qfn_and_version(
        data_row["qualified_name"]
    )
    data_currency = qn_tokens[2]
    data_model = qn_tokens[3]
    data_exchange = qn_tokens[0]
    data_target = qn_tokens[4]

    # Check for matching currency, model, and chart type
    is_matching_currency = data_currency == currency
    is_matching_model = data_model == model
    is_matching_exchange = data_exchange == exchange
    is_matching_target = data_target == target

    return (
        is_matching_currency
        and is_matching_model
        and is_matching_exchange
        and is_matching_target
    )


def ensure_column_exists(
    df: pd.DataFrame, column_name: str, default_value_factory: Callable[[], Any]
) -> None:
    """
    Ensure that the specified column exists in the DataFrame. If not, initialize it
    with the values produced by the default_value_factory for each row.

    Parameters:
        df (pandas.DataFrame): The DataFrame to modify.
        column_name (str): The name of the column to check.
        default_value_factory (callable): A function that returns the default value
                                          to initialize the column with.
    """
    if column_name not in df.columns:
        df[column_name] = [default_value_factory() for _ in range(len(df))]


def log_successful_addition_of_chart(chart: str, start_time: float) -> None:
    logging.info(
        f"Successfully Added {chart} to Dashboard in {time.time() - start_time}s"
    )
    return


def log_unsuccessful_addition_of_chart(chart: str, start_time: float) -> None:
    logging.exception(
        f"Unable to add {chart} to Dashboard in {time.time() - start_time}s"
    )
    return


# Helper function to convert expiry strings to datetime
def convert_expiry_to_datetime(expiry_str: str) -> datetime:
    return utils_general.from_iso(expiry_str)


def generate_date_range(
    start_date: str, end_date: str, freq: str
) -> pd.DatetimeIndex:
    return pd.date_range(start=start_date, end=end_date, freq=freq)


def encode_figure(fig: go.Figure) -> str:
    # Convert the figure to a PNG image in memory
    img_bytes = fig.to_image(format="png")
    # Encode the bytes to a base64 string
    encoded = base64.b64encode(img_bytes).decode("ascii")

    return encoded


def save_image(file_name: str, figure: go.Figure) -> None:
    cwd = Path.cwd()
    filepath = cwd / f"report_images/{file_name}"
    filepath.parent.mkdir(parents=True, exist_ok=True)
    figure.write_image(filepath, format="png")


def is_lambda_context() -> bool:
    return os.environ.get(LAMBDA_CONTEXT_ENV_NAME) == "True"
