import logging
import time

import pandas as pd
import utils_general

from .typings import (
    CalculationPipeline,
    FlexTimeseriesChart,
    FlexTimeSeriesTarget,
    SeriesReturnValues,
    WingSpreadPreppedData,
)


def extract_latest_points_for_targets(
    data_df: pd.DataFrame, plot_targets: list[FlexTimeSeriesTarget]
) -> dict[str, list[SeriesReturnValues]]:
    """
    Given a calculator output DataFrame and the corresponding plot targets,
    extract the latest non-null datapoint for each target and return a mapping
    from a human-readable label to a list of SeriesReturnValues dictionaries:
        [{"value": float, "timestamp": int}]

    The label preference order is: trace_title (if present) else target key.
    """
    latest_points: dict[str, list[SeriesReturnValues]] = {}
    for tgt in plot_targets:
        try:
            target_key = tgt["target"]
            label = tgt.get("trace_title", target_key) or target_key
            if target_key not in data_df.columns:
                continue

            series = data_df[target_key].dropna()
            if series.empty:
                continue

            series = series.sort_index()
            max_timestamp_idx = series.index.max()
            latest_value = float(series.loc[max_timestamp_idx])
            unix_timestamp_ns = int(max_timestamp_idx.value)
            latest_points[str(label)] = [
                {
                    "value": latest_value,
                    "timestamp": unix_timestamp_ns,
                }
            ]
        except Exception:
            logging.exception(
                "Failed extracting latest point for target=%s", tgt
            )

    return latest_points


def wing_spread_calc(
    prepared_data: WingSpreadPreppedData,
) -> tuple[pd.DataFrame, list[FlexTimeSeriesTarget]]:
    """
    Calculates the wing spread signals from prepared data.

    This function processes the given prepped data and computes wing spread
    signals. The wing spread values are computed as a standardized signal:
    take the spread (wing minus ATM), compare short- and long-window rolling means,
    and normalize by the long-window rolling standard deviation (z-score-like).

    Args:
        prepared_data (WingSpreadPreppedData): A dictionary where keys are qualified
            names of data series and values are dictionaries containing relevant
            time series data such as ATM volatility and wing volatility.

    Returns:
        tuple[pd.DataFrame, list[FlexTimeSeriesTarget]]: A tuple containing:
            1. A DataFrame with calculated wing spread signals and associated
               metadata.
            2. A list of corresponding FlexTimeSeriesTarget objects allowing to chart
                the wing spread signals.
    """
    st = time.time()

    smoothed_df = pd.DataFrame()

    for qn, data_series in prepared_data.items():
        qn_tokens = utils_general.get_qfn_and_version(qn)[1]
        tenor = qn_tokens[4]
        currency = qn_tokens[2]
        atm_vol = data_series["atm_vol"]

        # Process each wing target against ATM
        for wing_or_atm_target, vol_series in data_series.items():
            if wing_or_atm_target == "atm_vol":
                continue

            wing_target = wing_or_atm_target
            target_key = f"{currency}_{wing_target}_{tenor}"
            wing_vol = vol_series

            spread = wing_vol - atm_vol
            short = spread.rolling(window=4, min_periods=4).mean()
            long = spread.rolling(window=24 * 15, min_periods=24 * 15).mean()
            denom = (
                spread.rolling(window=24 * 15, min_periods=24 * 15)
                .std()
                .replace(0, pd.NA)
            )
            wing_spread = (short - long) / denom

            wing_spread_df = pd.DataFrame(wing_spread)
            # e.g v2composite.option.BTC.SVI.7d.{wing_target}.1h.wing_spread
            wing_spread_df["qualified_name"] = ".".join(
                [*qn_tokens[:5], wing_target, qn_tokens[5], "wing_spread"]
            )

            wing_spread_df.columns = pd.Index([target_key, "qualified_name"])
            wing_spread_df = wing_spread_df.dropna()
            smoothed_df = pd.concat([smoothed_df, wing_spread_df], axis=0)

    if smoothed_df.empty:
        logging.warning(
            "Wing spread calc produced empty data. Returning empty result."
        )
        return smoothed_df, []

    smoothed_df.index.name = "timestamp"

    plot_targets = _create_wing_spread_timeseries_targets(smoothed_df)

    logging.info(f"Computed wing spread in {time.time() - st}s")

    return smoothed_df, plot_targets


def _create_wing_spread_timeseries_targets(
    wing_spread_df: pd.DataFrame,
) -> list[FlexTimeSeriesTarget]:
    """
    Creates a list of FlexTimeSeriesTarget objects for the given wing spread dataframe.

    This function processes the input dataframe to identify target columns and their associated
    qualified names. It ensures that a single unique qualified name is associated with each target
    column and constructs FlexTimeSeriesTarget objects accordingly. If multiple qualified names
    are found for a single target column, a ValueError is raised.

    Args:
        wing_spread_df (pd.DataFrame): The input dataframe containing wing spread data. It must have
            columns for targets and a column named "qualified_name" which associates a unique
            identifier to the target series.

    Returns:
        list[FlexTimeSeriesTarget]: A list of FlexTimeSeriesTarget objects created based on the input
            dataframe.

    Raises:
        ValueError: If multiple qualified names are found for a single target column.
    """
    plot_targets: list[FlexTimeSeriesTarget] = []
    target_keys = [
        col for col in wing_spread_df.columns if col != "qualified_name"
    ]
    for _target_key in target_keys:
        qualified_series = (
            wing_spread_df[["qualified_name", _target_key]]
            .dropna()["qualified_name"]
            .unique()
        )
        if len(qualified_series) == 0:
            continue

        if len(qualified_series) != 1:
            raise ValueError(
                f"Multiple qualified names found for target {_target_key}: {qualified_series}"
            )
        qualified_name_value = qualified_series[0]
        ft_target = FlexTimeSeriesTarget(
            qualified_name=str(qualified_name_value),
            target=_target_key,
            trace_title=_target_key,
        )
        plot_targets.append(ft_target)

    return plot_targets


def _prepare_wing_spread_data(
    data_df: pd.DataFrame, chart_items: FlexTimeseriesChart
) -> WingSpreadPreppedData:
    """
    Prepares and processes data required for wing spread calculations, including grouping
    targets by qualified names, validating necessary data, and resampling time series for
    each qualified name and its associated targets. This function handles ATM and wing
    targets to produce a structured output suitable for further analysis.

    Args:
        data_df (pd.DataFrame): A DataFrame containing the input data with potential columns
            for qualified names and targets.
        chart_items (FlexTimeseriesChart): An object containing chart configuration, targets,
            and metadata.

    Returns:
        WingSpreadPreppedData: A dictionary with prepared data for each qualified name,
        where keys represent qualified names and values are dictionaries containing `atm_vol`
        and individual wing target data as resampled pandas Series.

    Raises:
        ValueError: If there are zero or multiple ATM targets or no wing targets for a
            qualified name. Also raised if required columns for a particular qualified name
            are missing.

    Warns:
        UserWarning: Logs warnings for qualified names without data or where sufficient
            resampled data doesn't meet requirements for calculations.
    """
    prepared_data: WingSpreadPreppedData = {}

    # Group targets by qualified_name to handle multiple targets per QN
    # qualified_name -> targets ([atm, -10delta])
    qn_targets: dict[str, list[str]] = {}
    for chart_target in chart_items["targets"]:
        qn = chart_target["qualified_name"]
        if qn not in qn_targets:
            qn_targets[qn] = []
        qn_targets[qn].append(chart_target["target"])

    for qn, targets in qn_targets.items():
        if len(targets) != len(set(targets)):
            duplicates = [
                target for target in set(targets) if targets.count(target) > 1
            ]
            logging.warning(
                f"Found duplicate targets for qualified name {qn}: {duplicates}. "
                f"Chart title: {chart_items['chart_title']}. Duplicates will be ignored."
            )

        unique_targets = set(targets)

        # Find ATM and wing targets
        atm_targets = [t for t in unique_targets if str(t).lower() == "atm"]
        wing_targets = [t for t in unique_targets if str(t).lower() != "atm"]

        # Validate exactly one ATM target
        if not atm_targets:
            raise ValueError(
                f"Wing spread calculation requires exactly 1 ATM target for {chart_items['chart_title']=}, {qn=}, got {len(atm_targets)=}"
            )

        # Validate at least one wing target
        if len(wing_targets) == 0:
            raise ValueError(
                f"Wing spread calculation requires at least 1 wing target for {chart_items['chart_title']=}, {qn=}, got {len(wing_targets)=}"
            )

        atm_target = atm_targets[0]

        # Filter data for this qualified name
        qn_data = data_df[
            data_df["qualified_name"].str.contains(qn)
        ]  # qn is unversioned
        if qn_data.empty:
            logging.warning(
                f"No data found for qualified name: {qn}, {chart_items['chart_title']}"
            )
            continue

        # Validate required columns exist
        missing_cols = [
            col for col in unique_targets if col not in qn_data.columns
        ]
        if missing_cols:
            raise ValueError(
                f"Missing columns {missing_cols} for {qn} for {chart_items['chart_title']}"
            )

        # Prepare ATM data
        atm_vol = qn_data[atm_target].resample("h").first().ffill()
        if atm_vol.empty:
            logging.warning(f"Insufficient ATM data for {qn} after resampling")
            continue

        # Prepare data structure for this qualified name
        qn_prepared_data = {"atm_vol": atm_vol}

        # Prepare wing data for each wing target
        valid_wing_targets = []
        for wing_target in wing_targets:
            wing_vol = qn_data[wing_target].resample("h").first().ffill()
            if wing_vol.empty:
                logging.warning(
                    f"Insufficient {wing_target} data for {qn} after resampling"
                )
                continue
            qn_prepared_data[wing_target] = wing_vol
            valid_wing_targets.append(wing_target)

        # Only add to prepared_data if we have at least one valid wing target
        if valid_wing_targets:
            prepared_data[qn] = qn_prepared_data
            logging.info(
                f"Prepared wing spread data for {qn}: atm={atm_target}, wings={valid_wing_targets}"
            )

    return prepared_data


CALCULATION_PIPELINES: dict[str, CalculationPipeline[WingSpreadPreppedData]] = {
    "WING_SPREAD_CALC": CalculationPipeline(
        preparator=_prepare_wing_spread_data,
        calculator=wing_spread_calc,
        data_extractor=extract_latest_points_for_targets,
    )
}
