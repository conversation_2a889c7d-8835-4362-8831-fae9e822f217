import itertools
import logging
import re
from datetime import UTC, datetime

import numpy as np
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import utils_calc
import utils_general
from plotly.subplots import make_subplots

from .constants import (
    MIN_TENOR_SIGNAL_MAPPING,
    PERPETUAL_TIME_SERIES_COLOURS,
    STRIKES_DICT,
    TENOR_COLORS,
    VOL_TERM_STRUCTURE_COLORS,
    YIELDS_TERM_STRUCTURE_COLORS,
)
from .utils import (
    _model_vol,
    format_unix_timestamp_ns,
    is_lambda_context,
    round_down,
    round_up,
    save_image,
)
from .utils_plot import (
    bs_standard_smile_layout,
    bs_standard_term_structure_layout,
    bs_standard_timeseries_layout,
    generate_xaxis_ticks,
)


def make_atm_vol_spread(
    data_df: pd.DataFrame,
    tenors: list[int],
    model: str,
    currency: str,
    series_to_plot: str,
    end_time: int,
) -> go.Figure:
    fig = make_subplots(specs=[[{"secondary_y": True}]])
    tenor_combinations = itertools.combinations(tenors, 2)

    currency_df = data_df[
        data_df["qualified_name"].str.contains("smile")
        & data_df["qualified_name"].str.contains(currency)
        & data_df["qualified_name"].str.contains(model)
    ]

    vol_spreads_df = pd.DataFrame()
    vol_spreads_df.index = currency_df.index

    for tenor_combination in tenor_combinations:
        min_tenor = min(tenor_combination)
        max_tenor = max(tenor_combination)

        min_tenor_timeseries = currency_df[
            currency_df["tenor_days"] == min_tenor
        ]
        max_tenor_timeseries = currency_df[
            currency_df["tenor_days"] == max_tenor
        ]

        multiplier = MIN_TENOR_SIGNAL_MAPPING[min_tenor][max_tenor]

        vol_ratio_time_series = (
            min_tenor_timeseries.loc[:, series_to_plot]
            - max_tenor_timeseries.loc[:, series_to_plot]
        )
        vol_spread_time_series = vol_ratio_time_series - multiplier
        new_column_df = vol_spread_time_series.to_frame(
            name=f"{min_tenor}-{max_tenor}"
        )
        vol_spreads_df = vol_spreads_df.combine_first(new_column_df)

        fig.add_trace(
            go.Scatter(
                mode="lines",
                x=vol_spread_time_series.index,
                y=(vol_spread_time_series.values) * 100,
                name=f"{min_tenor} vs {max_tenor}",
                line={
                    "width": 1,
                    "color": TENOR_COLORS[f"({min_tenor}, {max_tenor})"],
                },
            )
        )

    fig.add_shape(
        go.layout.Shape(
            type="line",
            x0=data_df.index.min(),
            y0=0,
            x1=data_df.index.max(),
            y1=0,
            line={
                "color": "red",
                "width": 2,
                "dash": "dot",
            },
        )
    )

    ytitle = ""
    fig = bs_standard_timeseries_layout(
        figure=fig,
        ytitle=ytitle,
        end_time=end_time,
        range_slider=False,
    )

    filename = f"{currency}-{model} ATM vol spread.png"
    if not is_lambda_context():
        save_image(filename, fig)
    return fig


def make_perpetual_time_series_fig(
    data_df: pd.DataFrame,
    currency: str,
    series_to_plot: str,
    end_time: int,
) -> go.Figure:
    fig = make_subplots(specs=[[{"secondary_y": True}]])

    currency_df = data_df[data_df["qualified_name"].str.contains(currency)]
    unique_qns = currency_df["qualified_name"].unique().tolist()

    for qn in unique_qns:
        currency = qn.split(".")[2].replace("-PERPETUAL", "")
        qn_df = currency_df[currency_df["qualified_name"] == qn]
        fig.add_trace(
            go.Scatter(
                mode="lines",
                x=qn_df.index,
                y=qn_df[series_to_plot] * 100,
                name=f"{currency}",
                line={"color": PERPETUAL_TIME_SERIES_COLOURS[currency]},
            )
        )

    fig = bs_standard_timeseries_layout(
        figure=fig, ytitle="timeseries", end_time=end_time, range_slider=False
    )

    tickvals = [
        i / 100
        for i in range(
            int(round_down((currency_df[series_to_plot] * 10_000).min(), 1.0)),
            int(round_up((currency_df[series_to_plot] * 10_000).max(), 1.0)),
            1,
        )
    ]
    ticktext = [f"{i}%" for i in tickvals]
    fig.update_layout(
        yaxis={
            "title": "Funding Rate",
            "tickmode": "array",
            "tickvals": tickvals,
            "ticktext": ticktext,
        }
    )
    filename = f"F{3 if currency == 'BTC' else 4}_{currency} Perpetual.png"
    if not is_lambda_context():
        save_image(filename, fig)
    return fig


def make_vol_term_structure_fig(
    data_df: pd.DataFrame,
    model: str,
    currency: str,
) -> go.Figure:
    unique_snapshots = data_df.index.unique().tolist()

    currency_df = data_df[
        data_df["qualified_name"].str.contains("smile")
        & data_df["qualified_name"].str.contains(currency)
    ]

    fig = make_subplots(specs=[[{"secondary_y": True}]])
    for idx, snapshot in enumerate(sorted(unique_snapshots)):
        snapshot_currency_df = currency_df.loc[snapshot, :].sort_values(
            "tenor_days"
        )
        color = VOL_TERM_STRUCTURE_COLORS[f"{idx}-{currency}"]
        if idx == 0:
            fig.add_trace(
                go.Scatter(
                    mode="lines+markers",
                    x=snapshot_currency_df["tenor_days"],
                    y=snapshot_currency_df["atm"],
                    name=f"{snapshot}",
                    line={"shape": "spline", "color": color},
                )
            )
        if idx == 1:
            fig.add_trace(
                go.Scatter(
                    mode="lines+markers",
                    x=snapshot_currency_df["tenor_days"],
                    y=snapshot_currency_df["atm"],
                    name=f"{snapshot}",
                    line={"shape": "spline", "color": color},
                )
            )
        if idx == 2:
            fig.add_trace(
                go.Scatter(
                    mode="lines+markers",
                    x=snapshot_currency_df["tenor_days"],
                    y=snapshot_currency_df["atm"],
                    name=f"{snapshot}",
                    line={"shape": "spline", "color": color},
                )
            )

    max_vol = currency_df["atm"].max()
    min_vol = currency_df["atm"].min()

    fig = bs_standard_term_structure_layout(
        figure=fig,
        vol_range=(
            round_down(min_vol, round_to_nearest=0.05),
            round_up(max_vol, round_to_nearest=0.05),
        ),
        strike_range=(0, 100),
    )
    fig.update_layout(
        yaxis_tickformat=".0%",
        showlegend=True,
    )
    filename = f"F{8 if currency == 'BTC' else 11} {currency} {model} Vol term structure.png"
    if not is_lambda_context():
        save_image(filename, fig)
    return fig


def make_futures_term_structure_fig(
    data_df: pd.DataFrame,
    currencies: list[str],
) -> go.Figure:
    data_df["pct"] = data_df["pct"].astype(float)
    qn_token_cols = [
        "exchange",
        "future",
        "ccy",
        "tenor",
        "freq",
        "annual",
        "percent",
    ]
    if "v" in data_df["qualified_name"].iloc[0]:
        qn_token_cols.insert(0, "version")

    data_df[qn_token_cols] = data_df["qualified_name"].str.split(
        ".", expand=True
    )
    data_df["tenor"] = data_df["tenor"].str.replace("d", "").astype(int)

    assert data_df.index.nunique() == 1

    fig = make_subplots(specs=[[{"secondary_y": True}]])
    for _idx, currency in enumerate(currencies):
        currency_df = data_df[
            data_df["qualified_name"].str.contains(currency)
        ].sort_values("tenor")
        if "BTC" in currency:
            fig.add_trace(
                go.Scatter(
                    mode="lines+markers",
                    x=currency_df["tenor"],
                    y=currency_df["pct"],
                    name=f"{currency}",
                    line={
                        "shape": "spline",
                        "color": YIELDS_TERM_STRUCTURE_COLORS[currency],
                    },
                )
            )
        if "ETH" in currency:
            fig.add_trace(
                go.Scatter(
                    mode="lines+markers",
                    x=currency_df["tenor"],
                    y=currency_df["pct"],
                    name=f"{currency}",
                    line={
                        "shape": "spline",
                        "color": YIELDS_TERM_STRUCTURE_COLORS[currency],
                    },
                )
            )

    title = data_df.index.tolist()[0]
    min_val = data_df["pct"].min()
    max_val = data_df["pct"].max()
    fig = bs_standard_term_structure_layout(
        figure=fig,
        vol_range=(
            round_down(min_val, round_to_nearest=0.05),
            round_up(max_val, round_to_nearest=0.05),
        ),
        strike_range=(0, 300),
        title=f"{title} UTC Snapshot",
    )
    fig.update_layout(
        yaxis_tickformat=".0%",
        showlegend=True,
        yaxis_title="Spot Yields",
    )
    filename = "F5_Futures term structure.png"
    if not is_lambda_context():
        save_image(filename, fig)
    return fig


def make_smile_constant_tenor_fig(
    data_df: pd.DataFrame,
    model: utils_calc.Model,
    currency: str,
) -> go.Figure:
    """
    This function is made specifically for historical smile plots and can handle at most, 2
    smiles. This function will need to be updated/modified if more functionality is needed.
    """
    fig = px.line()

    def _estimate_strike(delta: float) -> float:
        ini = None
        try:
            ini = utils_calc.estimate_strike_from_delta(
                model=model,
                model_params=params_row,
                s=params_row["spot"],
                f=params_row["forward"],
                t=params_row["expiry"],
                delta=delta,
                rd=0,
            )
        except Exception as e:
            logging.exception(
                f"Error calculating {model} delta. Unable to estimate strikes, {delta=:} {e=:}"
            )
        return ini

    min_delta = -0.01
    max_delta = 0.01

    constant_tenor_df = data_df[
        pd.isna(data_df["params"])
        & data_df["qualified_name"].str.contains(currency)
        & data_df["qualified_name"].str.contains(model)
    ]
    if constant_tenor_df.empty:
        logging.warning(
            f"Constant tenor Figure is empty for {model=}, {currency=}"
        )
        return fig

    unique_snapshots = constant_tenor_df["timestamp"].unique().tolist()

    currency_strikes = STRIKES_DICT[currency]
    constant_tenor_vol_range: tuple[float, float] = (0, 0)

    unique_snapshots.sort()

    for idx, snapshot in enumerate(unique_snapshots):
        constant_tenor_snapshot = constant_tenor_df[
            constant_tenor_df["timestamp"] == snapshot
        ].reset_index(drop=True)
        for _, params_row in constant_tenor_snapshot.iterrows():
            formatted_date = format_unix_timestamp_ns(params_row["timestamp"])
            constant_tenor_vols = np.asarray(
                _model_vol(params_row, model, np.asarray(currency_strikes))
            )
            # Todo: Remove dependencies on the below if statements
            if idx == 0:
                # older snapshot
                fig.add_trace(
                    go.Scatter(
                        mode="lines",
                        x=currency_strikes,
                        y=constant_tenor_vols,
                        name=formatted_date,
                        line={"color": "#6C6C6C", "dash": "dot"},
                    )
                )
            elif idx == 1:
                # more recent snapshot
                fig.add_trace(
                    go.Scatter(
                        mode="lines",
                        x=currency_strikes,
                        y=constant_tenor_vols,
                        name=formatted_date,
                        line={"color": "#004198"},
                    )
                )

                min_strike = _estimate_strike(min_delta)
                max_strike = _estimate_strike(max_delta)
                if not all([min_strike, max_strike]):
                    constant_tenor_vol_range = (min_strike, max_strike)
                    logging.warning(
                        "Failed to estimate deltas, falling back to standard vol range"
                    )
                else:
                    constant_tenor_vol_range = (
                        0.2 * params_row["forward"],
                        2.0 * params_row["forward"],
                    )

    tenor: int = int(constant_tenor_snapshot.iloc[0]["tenor_days"])
    unix_time: int = unique_snapshots[0]
    dt = datetime.fromtimestamp(unix_time / 1e9, tz=UTC)
    time_formatted = dt.strftime("%H:%M")

    fig = bs_standard_smile_layout(
        figure=fig,
        title=f"{currency} {model}, {tenor} Day Tenor, {time_formatted} UTC Snapshot",
        vol_range=(round_down(min(constant_tenor_vols) - 0.1, 0.05), 1.4),
        strike_range=constant_tenor_vol_range,
    )
    if currency == "BTC":
        fig.update_layout(xaxis=generate_xaxis_ticks(0, 180_000, 10_000))
    elif currency == "ETH":
        fig.update_layout(xaxis=generate_xaxis_ticks(0, 10_000, 500))

    filename = f"F{24 if currency == 'BTC' else 25}_{currency}-{model} Constant Maturity.png"
    if not is_lambda_context():
        save_image(filename, fig)
    return fig


def make_smile_listed_expiry_fig(
    data_df: pd.DataFrame,
    currency: str,
    expiry_to_plot: str,
    exchange: str,
) -> go.Figure:
    """
    This function is made specifically for historical smile plots and can handle at most, 2
    smiles. This function will need to be updated/modified if more functionality is needed.

    """

    def _fix_underlying_index_(underlying_index: str, exchange: str) -> str:
        underlying_index = underlying_index.replace("SYN.", "")
        iso_date_match = re.search(r"\d{4}-\d{2}-\d{2}", underlying_index)

        if iso_date_match:
            # Convert the matched date to the required format
            iso_date = iso_date_match.group(0)
            formatted_date = (
                datetime.strptime(iso_date, "%Y-%m-%d")
                .replace(tzinfo=UTC)
                .strftime("%-d%b%y")
            )

            # Replace the ISO date with the formatted date and return the result
            return re.sub(
                r"\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z",
                formatted_date,
                underlying_index,
            ).upper()

        instrument_fields = utils_general.get_fields_from_instrument_name(
            instrument=underlying_index,
            instrument_type="future",
            exchange=exchange,
        )
        new_underlying_index = (
            instrument_fields["base"]
            + "-"
            + instrument_fields["uniform_expiry"]
        )

        return new_underlying_index

    def _find_indices(
        subset_list: list[float], full_list: list[float]
    ) -> list[float]:
        """
        Finds the indices of elements of subset_list in full_list.

        Parameters:
        subset_list (list): A list that is a subset of full_list.
        full_list (list): A list that contains all elements of subset_list.

        Returns:
        list: A list of indices where elements of subset_list are found in full_list.
        """
        indices = []
        for element in subset_list:
            if element in full_list:
                index = full_list.index(element)
                indices.append(index)
            else:
                raise ValueError(
                    f"Element '{element}' from subset_list not found in full_list."
                )
        return indices

    def _extract_values_at_indices(
        indices: list[float], values_list: list[float]
    ):
        """
        Extracts values from values_list at the specified indices.

        Parameters:
        indices (list): A list of indices.
        values_list (list): A list of values from which to extract.

        Returns:
        list: A list of values at the specified indices.
        """
        extracted_values = [values_list[i] for i in indices]
        return extracted_values

    qn_token_cols = [
        "exchange",
        "option",
        "ccy",
        "model",
        "freq",
        "calc_type",
    ]
    if "v-" in data_df["qualified_name"].iloc[0]:
        qn_token_cols.insert(0, "version")
    currency_strikes = STRIKES_DICT[currency]
    relevant_underlying_index = ("-").join([currency, expiry_to_plot])

    listed_tenor_df = data_df[
        ~pd.isna(data_df["params"])
        & data_df["qualified_name"].str.contains(currency)
        # & data_df["qualified_name"].str.contains(model)
    ]
    MODEL_LINE_CONFIG = {
        "SVI": {"color": "#6C6C6C", "dash": "dot"},
        "SABR": {"color": "#004198"},
    }

    to_concat = []
    assert listed_tenor_df["timestamp"].nunique() == 1
    timestamp = listed_tenor_df.iloc[0]["timestamp"]

    for _idx, row in listed_tenor_df.iterrows():
        params_df = pd.DataFrame(row["params"])
        params_df["qualified_name"] = row["qualified_name"]
        to_concat.append(params_df)

    listed_exp_df = pd.concat(to_concat, ignore_index=True)
    listed_exp_df["underlying_index"] = listed_exp_df["underlying_index"].apply(
        _fix_underlying_index_, args=(exchange,)
    )
    relevant_listed_exp_df = listed_exp_df[
        listed_exp_df["underlying_index"] == relevant_underlying_index
    ].copy()
    relevant_listed_exp_df[qn_token_cols] = relevant_listed_exp_df[
        "qualified_name"
    ].str.split(".", expand=True)

    fig = px.line()
    any_listed_exp_row = relevant_listed_exp_df.iloc[0]
    if "v-" in data_df["qualified_name"].iloc[0]:
        version_int = int(any_listed_exp_row["version"].split("-")[-1])
        if version_int > 2:
            assert len(
                any_listed_exp_row["strikes_pre_delta_bound_filt"]
            ) == len(
                any_listed_exp_row["bidiv"],
            )
            indices = _find_indices(
                subset_list=any_listed_exp_row["strikescalib"],
                full_list=any_listed_exp_row["strikes_pre_delta_bound_filt"],
            )
            askiv = _extract_values_at_indices(
                indices=indices, values_list=any_listed_exp_row["askiv"]
            )
            bidiv = _extract_values_at_indices(
                indices=indices, values_list=any_listed_exp_row["bidiv"]
            )
        else:
            bidiv = any_listed_exp_row["bidiv"]
            askiv = any_listed_exp_row["askiv"]
    else:
        bidiv = any_listed_exp_row["bidiv"]
        askiv = any_listed_exp_row["askiv"]

    # add bid and ask strikes. They should be the same
    fig.add_trace(
        go.Scatter(
            x=any_listed_exp_row["strikescalib"],
            y=bidiv,
            mode="markers",
            name="Bid Price IV",
            marker={"color": "#FF2200"},
        )
    )
    fig.add_trace(
        go.Scatter(
            x=any_listed_exp_row["strikescalib"],
            y=askiv,
            mode="markers",
            name="Ask Price IV",
            marker={"color": "#0AE646"},
        )
    )
    for model in relevant_listed_exp_df["model"]:
        selected_model_params = relevant_listed_exp_df[
            relevant_listed_exp_df["model"] == model
        ].iloc[0]

        listed_tenor_strike_range: tuple[float, float] = (
            selected_model_params["forward"] * 0.2,
            selected_model_params["forward"] * 2.0,
        )
        vols = _model_vol(
            row=selected_model_params,
            model=model,
            strike=np.asarray(currency_strikes),
        )
        fig.add_trace(
            go.Scatter(
                mode="lines",
                x=currency_strikes,
                y=vols,
                name=model,
                line=MODEL_LINE_CONFIG[model],
            )
        )

    dt = pd.to_datetime(timestamp, unit="ns")
    parsed_underlying_index = datetime.strptime(
        expiry_to_plot, "%d%b%y"
    ).replace(tzinfo=UTC)
    expiry_date = parsed_underlying_index.strftime("%d-%b-%y")

    fig = bs_standard_smile_layout(
        figure=fig,
        vol_range=(round_down(min(vols) - 0.1, 0.05), 1.55),
        title=f"{currency} - {expiry_date} Expiry, {dt.strftime('%H:%M')} UTC Snapshot",
        strike_range=listed_tenor_strike_range,
    )

    if currency == "BTC":
        fig.update_layout(xaxis=generate_xaxis_ticks(0, 180_000, 10_000))
    elif currency == "ETH":
        fig.update_layout(xaxis=generate_xaxis_ticks(0, 10_000, 500))

    filename = f"{currency} {relevant_underlying_index} Listed Expiry.png"
    if not is_lambda_context():
        save_image(filename, fig)
    return fig
