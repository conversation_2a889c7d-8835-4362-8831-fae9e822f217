import numpy as np

from .typings import AggregationFilter<PERSON><PERSON><PERSON>, AggregationLevels

MIN_TENOR_SIGNAL_MAPPING = {
    7: {14: 0.13, 30: 0.29, 60: 0.39, 90: 0.42, 180: 0.50},
    14: {30: 0.13, 60: 0.25, 90: 0.31, 180: 0.49},
    30: {60: 0.12, 90: 0.18, 180: 0.28},
    60: {90: 0.06, 180: 0.20},
    90: {180: 0.09},
}

STRIKES_DICT = {
    "BTC": list(range(1, 180001, 200)),
    "ETH": list(range(1, 9001, 50)),
    "SOL": list(range(1, 600, 5)),
}
MONEYNESS_DENSE = np.asarray(list(range(10, 1000, 1))) / 100
CALL_DELTAS = np.asarray(list(range(0, 101, 1))) / 100

MONEYNESS = [
    0.1,
    0.2,
    0.3,
    0.4,
    0.5,
    0.6,
    0.7,
    0.8,
    0.9,
    1,
    1.1,
    1.2,
    1.3,
    1.4,
    1.5,
    1.6,
    1.7,
    1.8,
    1.9,
    2,
    2.1,
    2.2,
    2.3,
    2.4,
    2.5,
    2.6,
    2.7,
    2.8,
    2.9,
    3,
]

SINGLE_TENOR_COLORS = {
    7: "#247CFF",
    14: "#C5C5C5",
    30: "#FFCD00",
    60: "#FF54AF",
    90: "#E16100",
    120: "#c700ff",
    180: "#43FF64",
}

PERPETUAL_TIME_SERIES_COLOURS = {
    "ETH": "#883CFF",
    "ETH_USDC": "#C49DFF",
    "BTC": "#FFCD00",
    "BTC_USDC": "#F4F4B0",
}

VOL_TERM_STRUCTURE_COLORS = {
    "2-ETH": "#FF0000",
    "1-ETH": "#883CFF",
    "0-ETH": "#C49DFF",
    "2-BTC": "#FF0000",
    "1-BTC": "#FFCD00",
    "0-BTC": "#F4F4B0",
    "2-SOL": "#FF0000",
    "1-SOL": "#008B8B",
    "0-SOL": "#00CED1",
}

YIELDS_TERM_STRUCTURE_COLORS = {
    "ETH": "#883CFF",
    "BTC": "#FFCD00",
    "ETH_USDC": "#883CFF",
    "BTC_USDC": "#FFCD00",
    "ETH_USD": "#883CFF",
    "BTC_USD": "#FFCD00",
}

TENOR_COLORS = {
    # Orange palette for '7'
    "(7, 14)": "#FF8C00",  # Vivid orange
    "(7, 30)": "#FFA500",  # Brighter orange
    "(7, 60)": "#FFB732",  # Light orange
    "(7, 90)": "#FFC966",  # Lighter orange
    "(7, 180)": "#FFDB99",  # Pale orange
    # Purple palette for '14'
    "(14, 30)": "#800080",  # Dark purple
    "(14, 60)": "#9370DB",  # Medium purple
    "(14, 90)": "#AB82FF",  # Light purple
    "(14, 180)": "#C9A0DC",  # Pale purple
    # Green palette for '30'
    "(30, 60)": "#2E8B57",  # Dark green
    "(30, 90)": "#3CB371",  # Medium green
    "(30, 180)": "#98FB98",  # Pale green
    # Blue palette for '60'
    "(60, 90)": "#1E90FF",  # Bright blue
    "(60, 180)": "#6495ED",  # Cornflower blue
    # Yellow-Green palette for '90'
    "(90, 180)": "#9ACD32",  # Yellow-green
}

EXCHANGE_COLORS = {
    "deribit": "#FF2200",
    "bybit": "#F7A600",
    "v2composite": "#247CFF",
    "v2lyra": "#25fab0",
    # "aevo": ,
    "okx": "#1E90FF",
}


# CURRENCY_COLORS = {"BTC": "#F7931A", "ETH": "#8A92B2"}
UNVERSIONED_INSTRUMENTS = {"perpetual", "future", "spot"}


STATIC_EVENT_COLORS = {
    "#_DERIBIT_COLOR": "#FF2200",
    "#_BYBIT_COLOR": "#F7A600",
    "#_V2COMPOSITE_COLOR": "#247CFF",
    "#_V2LYRA_COLOR": "#25fab0",
    "#_OKX_COLOR": "#9370DB",
    "#_PERP_BTC_USD_COLOR": "#FFCD00",
    "#_PERP_BTC_USDC_COLOR": "#F4F4B0",
    "#_PERP_ETH_USD_COLOR": "#883CFF",
    "#_PERP_ETH_USDC_COLOR": "#C49DFF",
    "#_7_": "#247CFF",
    # 14: "#C5C5C5",
    "#_14_": "#C5C5C5",
    # 30: "#C5C5C5",
    "#_30_": "#FFCD00",
    "#_60_": "#E16100",
    "#_90_": "#E16100",
    "#_180_": "#43FF64",
}


QN_SUFFIX_MAPPING = {"volume": "volume.sum", "open_interest": "oi"}
QN_SUFFIX_MAPPING_TICK = {"volume": "volume.amt", "open_interest": "oi"}
FILTER_MAPPING: dict[AggregationFilterKeys, AggregationLevels] = {
    "exchanges": "exchange",
    "expiries": "expiry",
    "currencies": "currency",
    "instruments": "instrument",
    "option_types": "option_type",
}

BYBIT_PERP_INSTRUMENTS = [
    "DOGEUSDT",
    "SOLUSDT",
    "XRPUSDT",
    "ADAUSDT",
    "BNBUSDT",
    "NEARUSDT",
    "ATOMUSDT",
    "CRVUSDT",
    "TONUSDT",
]

FR_API_URL = "https://api.bybit.com/v5/market/funding/history"
OI_API_URL = "https://api.bybit.com/v5/market/open-interest"

LAMBDA_CONTEXT_ENV_NAME = "IS_LAMBDA_CONTEXT"
